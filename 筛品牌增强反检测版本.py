#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon产品筛选器 - 增强反检测版本
主要改进：
1. Cookie池管理系统 - 减少Selenium使用频率
2. 智能反检测机制 - 浏览器指纹轮换
3. 自适应延迟控制 - 基于成功率动态调整
4. 多策略语言转换 - 减少对Selenium的依赖
"""

import requests
import random
import time
import threading
import pickle
import os
import re
import urllib.parse
import hashlib
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

class EnhancedAmazonScraper:
    """增强版Amazon产品爬虫 - 集成反检测和Cookie池管理"""
    
    def __init__(self):
        print("🚀 初始化增强版Amazon爬虫...")
        
        # 基础配置
        self.session = requests.Session()
        self.lock = threading.Lock()
        self.stop_event = threading.Event()
        
        # 代理管理
        self.proxies = self.load_proxies()
        self.invalid_proxies = []
        self.suspended_proxies = {}
        self.proxy_failures = {}
        
        # Cookie池管理
        self.cookie_pool = []
        self.cookie_pool_file = 'enhanced_cookie_pool.pkl'
        self.max_cookie_pool_size = 5
        self.cookie_refresh_interval = 3600  # 1小时
        self.last_cookie_refresh = 0
        
        # 反检测增强
        self.request_count = 0
        self.session_start_time = time.time()
        self.max_requests_per_session = 100
        self.session_duration_limit = 1800  # 30分钟
        
        # 浏览器指纹池
        self.fingerprint_pool = []
        self.current_fingerprint_index = 0
        self.generate_fingerprint_pool()
        
        # 智能延迟控制
        self.adaptive_delay = True
        self.base_delay = 1.0
        self.max_delay = 5.0
        self.success_streak = 0
        self.failure_streak = 0
        
        # 结果存储
        self.valid_results = []
        self.processed_asins = set()
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # 初始化系统
        self.load_cookie_pool()
        self.apply_fingerprint(self.get_next_fingerprint())

        # 预设美国地理位置Cookie
        self.set_us_location_cookies()
        print("🇺🇸 已预设美国地理位置Cookie")

        print("✅ 增强反检测系统初始化完成")
    
    def load_proxies(self):
        """加载代理列表"""
        proxies = []
        try:
            if os.path.exists('proxies.txt'):
                with open('proxies.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # 解析代理格式: socks5://用户名:密码@IP:端口
                            if '://' in line:
                                parts = line.split('://')
                                if len(parts) == 2:
                                    protocol = parts[0]
                                    auth_host = parts[1]
                                    
                                    if '@' in auth_host:
                                        auth, host_port = auth_host.split('@', 1)
                                        username, password = auth.split(':', 1)
                                        host, port = host_port.split(':', 1)
                                        
                                        proxy = {
                                            'http': f'{protocol}://{username}:{password}@{host}:{port}',
                                            'https': f'{protocol}://{username}:{password}@{host}:{port}'
                                        }
                                        proxies.append(proxy)
                print(f"📡 加载了 {len(proxies)} 个代理")
            else:
                print("⚠️ 代理文件不存在，将使用直连")
        except Exception as e:
            print(f"❌ 加载代理失败: {str(e)}")
        
        return proxies
    
    def generate_fingerprint_pool(self):
        """生成浏览器指纹池"""
        print("🔍 生成浏览器指纹池...")
        
        # 常见的屏幕分辨率
        screen_resolutions = [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024)
        ]
        
        # 常见的操作系统和浏览器组合
        os_browser_combos = [
            ("Windows NT 10.0; Win64; x64", "Chrome", random.randint(100, 120)),
            ("Windows NT 10.0; Win64; x64", "Edge", random.randint(100, 120)),
            ("Macintosh; Intel Mac OS X 10_15_7", "Chrome", random.randint(100, 120)),
            ("Macintosh; Intel Mac OS X 10_15_7", "Safari", random.randint(14, 16)),
            ("X11; Linux x86_64", "Chrome", random.randint(100, 120)),
            ("X11; Ubuntu; Linux x86_64", "Firefox", random.randint(100, 120))
        ]
        
        for i in range(10):  # 生成10个不同的指纹
            os_info, browser, version = random.choice(os_browser_combos)
            width, height = random.choice(screen_resolutions)
            
            if browser == "Chrome":
                user_agent = f"Mozilla/5.0 ({os_info}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version}.0.0.0 Safari/537.36"
            elif browser == "Edge":
                user_agent = f"Mozilla/5.0 ({os_info}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version}.0.0.0 Edg/{version}.0.0.0"
            elif browser == "Safari":
                user_agent = f"Mozilla/5.0 ({os_info}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{version}.0 Safari/605.1.15"
            elif browser == "Firefox":
                user_agent = f"Mozilla/5.0 ({os_info}; rv:{version}.0) Gecko/20100101 Firefox/{version}.0"
            else:
                user_agent = f"Mozilla/5.0 ({os_info}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version}.0.0.0 Safari/537.36"
            
            fingerprint = {
                'user_agent': user_agent,
                'screen_width': width,
                'screen_height': height,
                'color_depth': random.choice([24, 32]),
                'timezone_offset': random.choice([-480, -420, -360, -300, -240, -180, -120, 0]),
                'language': random.choice(['en-US', 'en-GB', 'en-CA']),
                'platform': os_info.split(';')[0] if ';' in os_info else os_info,
                'do_not_track': random.choice(['1', '0', None]),
                'accept_language': random.choice([
                    'en-US,en;q=0.9',
                    'en-GB,en;q=0.9,en-US;q=0.8',
                    'en-US,en;q=0.9,zh-CN;q=0.8'
                ])
            }
            
            self.fingerprint_pool.append(fingerprint)
        
        print(f"✅ 生成了 {len(self.fingerprint_pool)} 个浏览器指纹")
    
    def get_next_fingerprint(self):
        """获取下一个浏览器指纹"""
        if not self.fingerprint_pool:
            self.generate_fingerprint_pool()
        
        fingerprint = self.fingerprint_pool[self.current_fingerprint_index]
        self.current_fingerprint_index = (self.current_fingerprint_index + 1) % len(self.fingerprint_pool)
        
        return fingerprint
    
    def apply_fingerprint(self, fingerprint):
        """应用浏览器指纹到会话"""
        headers = {
            'User-Agent': fingerprint['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': fingerprint['accept_language'],
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': fingerprint['do_not_track'] or '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'Sec-Ch-Ua': f'"Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': f'"{fingerprint["platform"]}"',
        }
        
        self.session.headers.update(headers)
        print(f"🎭 应用新指纹: {fingerprint['user_agent'][:50]}...")
    
    def md5_encrypt(self, text):
        """MD5加密辅助方法"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def get_proxy_info(self, proxy):
        """获取代理信息字符串"""
        if not proxy:
            return "直连"
        
        try:
            proxy_url = proxy.get('http', proxy.get('https', ''))
            if '://' in proxy_url:
                parts = proxy_url.split('://')
                if len(parts) == 2:
                    protocol = parts[0]
                    auth_host = parts[1]
                    if '@' in auth_host:
                        auth, host_port = auth_host.split('@', 1)
                        return f"{protocol}://{host_port}"
                    else:
                        return f"{protocol}://{auth_host}"
            return str(proxy)
        except:
            return "未知代理"
    
    def get_available_proxy(self):
        """获取可用的代理，避开暂停的代理"""
        if not self.proxies:
            return None
        
        current_time = time.time()
        available_proxies = []
        
        for proxy in self.proxies:
            proxy_str = self.get_proxy_info(proxy)
            
            # 检查代理是否在暂停期
            if proxy_str in self.suspended_proxies:
                suspend_time = self.suspended_proxies[proxy_str]
                if current_time - suspend_time < 300:  # 5分钟暂停期
                    continue
                else:
                    # 暂停期结束，移除暂停状态
                    del self.suspended_proxies[proxy_str]
            
            available_proxies.append(proxy)
        
        if available_proxies:
            return random.choice(available_proxies)
        else:
            print("⚠️ 所有代理都在暂停期，使用第一个代理")
            return self.proxies[0] if self.proxies else None

    # ===== Cookie池管理方法 =====

    def load_cookie_pool(self):
        """加载Cookie池"""
        try:
            if os.path.exists(self.cookie_pool_file):
                with open(self.cookie_pool_file, 'rb') as f:
                    pool_data = pickle.load(f)

                # 检查Cookie是否过期
                current_time = time.time()
                valid_cookies = []

                for cookie_data in pool_data:
                    if current_time - cookie_data['timestamp'] < 24 * 60 * 60:  # 24小时内有效
                        valid_cookies.append(cookie_data)

                self.cookie_pool = valid_cookies
                print(f"📦 加载了 {len(self.cookie_pool)} 个有效Cookie")

                # 如果Cookie池不足，尝试补充
                if len(self.cookie_pool) < 2:
                    print("Cookie池数量不足，开始补充...")
                    self.refresh_cookie_pool()
            else:
                print("Cookie池文件不存在，开始创建...")
                self.refresh_cookie_pool()

        except Exception as e:
            print(f"加载Cookie池时出错: {str(e)}")
            self.cookie_pool = []
            self.refresh_cookie_pool()

    def save_cookie_pool(self):
        """保存Cookie池到文件"""
        try:
            with open(self.cookie_pool_file, 'wb') as f:
                pickle.dump(self.cookie_pool, f)
            print(f"💾 Cookie池已保存，包含 {len(self.cookie_pool)} 个Cookie")
        except Exception as e:
            print(f"保存Cookie池时出错: {str(e)}")

    def refresh_cookie_pool(self):
        """刷新Cookie池，获取新的有效Cookie"""
        print("🔄 开始刷新Cookie池...")

        target_size = min(self.max_cookie_pool_size, 3)  # 至少获取3个Cookie
        current_size = len(self.cookie_pool)

        for i in range(target_size - current_size):
            print(f"获取第 {current_size + i + 1} 个Cookie...")

            if self.update_cookies_with_selenium():
                # 获取当前会话的Cookie
                cookie_dict = {}
                for cookie in self.session.cookies:
                    cookie_dict[cookie.name] = cookie.value

                # 添加到Cookie池
                cookie_data = {
                    'cookies': cookie_dict,
                    'timestamp': time.time(),
                    'user_agent': self.session.headers.get('User-Agent', ''),
                    'success_count': 0,
                    'failure_count': 0
                }

                self.cookie_pool.append(cookie_data)
                print(f"✅ 成功获取Cookie {current_size + i + 1}")

                # 短暂延迟避免频繁请求
                time.sleep(random.uniform(2, 5))
            else:
                print(f"❌ 获取Cookie {current_size + i + 1} 失败")
                break

        # 保存Cookie池
        self.save_cookie_pool()
        self.last_cookie_refresh = time.time()

        print(f"🎯 Cookie池刷新完成，当前包含 {len(self.cookie_pool)} 个Cookie")

    def get_best_cookie(self):
        """获取最佳的Cookie"""
        if not self.cookie_pool:
            print("Cookie池为空，尝试刷新...")
            self.refresh_cookie_pool()
            if not self.cookie_pool:
                return None

        # 按成功率排序，选择最佳Cookie
        best_cookie = min(self.cookie_pool,
                         key=lambda x: x['failure_count'] / max(x['success_count'] + x['failure_count'], 1))

        return best_cookie

    def update_cookie_stats(self, cookie_data, success=True):
        """更新Cookie统计信息"""
        if success:
            cookie_data['success_count'] += 1
            self.success_streak += 1
            self.failure_streak = 0
        else:
            cookie_data['failure_count'] += 1
            self.failure_streak += 1
            self.success_streak = 0

            # 如果失败次数过多，从池中移除
            if cookie_data['failure_count'] > 10:
                try:
                    self.cookie_pool.remove(cookie_data)
                    print(f"🗑️ 移除失效Cookie，剩余 {len(self.cookie_pool)} 个")
                except ValueError:
                    pass

    def update_cookies_with_selenium(self):
        """使用Selenium更新Cookie - 优化版本"""
        print("🤖 使用Selenium获取新Cookie...")

        driver = None
        try:
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

            # 应用当前指纹
            fingerprint = self.get_next_fingerprint()
            chrome_options.add_argument(f'--user-agent={fingerprint["user_agent"]}')

            # 添加代理支持
            proxy = self.get_available_proxy()
            if proxy:
                proxy_str = self.get_proxy_info(proxy)
                if 'socks5://' in str(proxy):
                    # 处理SOCKS5代理
                    proxy_url = proxy.get('http', proxy.get('https', ''))
                    if '@' in proxy_url:
                        auth_host = proxy_url.split('://', 1)[1]
                        auth, host_port = auth_host.split('@', 1)
                        chrome_options.add_argument(f'--proxy-server=socks5://{host_port}')

            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)

            # 访问Amazon首页建立会话
            print("🌐 访问Amazon首页...")
            driver.get("https://www.amazon.com/?language=en_US&country=US")

            # 等待页面加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 设置语言偏好Cookie
            driver.execute_script("""
                document.cookie = 'i18n-prefs=USD; domain=.amazon.com; path=/';
                document.cookie = 'lc-main=en_US; domain=.amazon.com; path=/';
                document.cookie = 'sp-cdn=L5Z9:US; domain=.amazon.com; path=/';
            """)

            # 短暂等待Cookie生效
            time.sleep(2)

            # 获取Cookie并更新到requests会话
            selenium_cookies = driver.get_cookies()
            for cookie in selenium_cookies:
                self.session.cookies.set(
                    cookie['name'],
                    cookie['value'],
                    domain=cookie.get('domain', '.amazon.com'),
                    path=cookie.get('path', '/')
                )

            print(f"✅ 成功获取 {len(selenium_cookies)} 个Cookie")
            return True

        except Exception as e:
            print(f"❌ Selenium获取Cookie失败: {str(e)}")
            return False
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    # ===== 智能延迟控制和会话管理 =====

    def calculate_adaptive_delay(self):
        """计算自适应延迟"""
        if not self.adaptive_delay:
            return random.uniform(self.base_delay, self.base_delay * 2)

        # 基于成功/失败率调整延迟
        if self.failure_streak > 3:
            # 连续失败，增加延迟
            delay = min(self.max_delay, self.base_delay * (1 + self.failure_streak * 0.5))
        elif self.success_streak > 5:
            # 连续成功，减少延迟
            delay = max(self.base_delay * 0.5, self.base_delay * (1 - self.success_streak * 0.1))
        else:
            # 正常延迟
            delay = self.base_delay

        # 添加随机化
        delay = random.uniform(delay * 0.8, delay * 1.2)

        return delay

    def should_refresh_session(self):
        """判断是否需要刷新会话"""
        current_time = time.time()

        # 检查请求数量限制
        if self.request_count >= self.max_requests_per_session:
            print(f"🔄 达到最大请求数 {self.max_requests_per_session}，需要刷新会话")
            return True

        # 检查会话持续时间
        if current_time - self.session_start_time >= self.session_duration_limit:
            print(f"🔄 会话持续时间超过 {self.session_duration_limit} 秒，需要刷新会话")
            return True

        # 检查Cookie池是否需要刷新
        if current_time - self.last_cookie_refresh >= self.cookie_refresh_interval:
            print("🔄 Cookie池需要刷新")
            return True

        return False

    def refresh_session(self):
        """刷新会话"""
        print("🔄 正在刷新会话...")

        # 重置计数器
        self.request_count = 0
        self.session_start_time = time.time()
        self.success_streak = 0
        self.failure_streak = 0

        # 创建新的会话
        self.session.close()
        self.session = requests.Session()

        # 应用新的浏览器指纹
        fingerprint = self.get_next_fingerprint()
        self.apply_fingerprint(fingerprint)

        # 刷新Cookie池（如果需要）
        current_time = time.time()
        if current_time - self.last_cookie_refresh >= self.cookie_refresh_interval:
            self.refresh_cookie_pool()

        print("✅ 会话刷新完成")

    def contains_chinese(self, text):
        """检测文本是否包含中文字符"""
        chinese_range = re.compile(u'[\u4e00-\u9fff]')
        return bool(chinese_range.search(text))

    def set_us_location_cookies(self):
        """设置美国地理位置相关的Cookie"""
        us_location_cookies = {
            # 核心地理位置Cookie
            'lc-main': 'en_US',
            'i18n-prefs': 'USD',
            'sp-cdn': 'L5Z9:US',

            # 地址和邮编相关
            'zipcode': '10001',  # 纽约邮编
            'location-main': 'US',
            'amazon-shopping-pref': 'language=en_US&country=US&zipcode=10001',

            # 货币和区域
            'currency': 'USD',
            'country': 'US',
            'marketplace-id': 'ATVPDKIKX0DER',  # 美国市场ID

            # 会话相关
            'session-id-time': str(int(time.time())),
            'ubid-main': f'13{random.randint(0,9)}-{random.randint(1000000,9999999)}-{random.randint(1000000,9999999)}',

            # 防检测相关
            'csm-hit': f'tb:s-{self.md5_encrypt(str(time.time()))[:16].upper()}|{int(time.time() * 1000)}&t:{int(time.time() * 1000)}&adb:adblk_no',
            'skin': 'noskin',

            # 强制英文设置
            'LanguagePreference': 'en_US',
            'amazon-language-preference': 'en_US'
        }

        for name, value in us_location_cookies.items():
            self.session.cookies.set(name, value, domain='.amazon.com', path='/')

    def get_us_headers(self):
        """获取美国地区的请求头"""
        # 生成美国IP地址
        us_ip = f"18.{random.randint(200, 250)}.{random.randint(1, 254)}.{random.randint(1, 254)}"

        headers = self.session.headers.copy()
        headers.update({
            # 语言设置
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Language': 'en-US',

            # 地理位置伪装
            'X-Forwarded-For': us_ip,
            'X-Real-IP': us_ip,
            'X-Country': 'US',
            'X-Locale': 'en_US',
            'X-Amazon-Country': 'US',

            # 时区设置
            'X-Timezone': 'America/New_York',

            # 其他区域设置
            'CloudFront-Viewer-Country': 'US',
            'CloudFront-Viewer-Country-Region': 'NY',
            'CloudFront-Viewer-City': 'New York'
        })

        return headers

    def force_english_page(self, response):
        """强制转换为英文页面 - 增强版本"""
        soup = BeautifulSoup(response.content, 'html.parser')
        title = soup.find('title')

        if title and self.contains_chinese(title.text):
            print("🌐 检测到中文页面，使用增强策略强制转换为英文...")

            # 策略1: 强化地理位置设置 + URL参数转换
            try:
                print("🔄 策略1: 强化地理位置设置...")

                # 设置完整的美国地理位置Cookie
                self.set_us_location_cookies()

                # 修改URL参数
                url_parts = list(urllib.parse.urlparse(response.url))
                query = dict(urllib.parse.parse_qsl(url_parts[4]))
                query.update({
                    'language': 'en_US',
                    'country': 'US',
                    'lc': 'en_US',
                    'ie': 'UTF8',
                    'ref_': 'nav_signin',
                    'LanguagePreference': 'en_US',
                    'zipcode': '10001'
                })
                url_parts[4] = urllib.parse.urlencode(query)
                new_url = urllib.parse.urlunparse(url_parts)

                # 使用美国地区请求头
                headers = self.get_us_headers()

                # 发起请求
                proxy = self.get_available_proxy()
                new_response = self.session.get(new_url, headers=headers, proxies=proxy, timeout=12)

                if new_response.status_code == 200:
                    new_soup = BeautifulSoup(new_response.content, 'html.parser')
                    new_title = new_soup.find('title')

                    if new_title and not self.contains_chinese(new_title.text):
                        print("✅ 策略1成功: 页面已转换为英文")
                        return new_response
                    else:
                        print("❌ 策略1失败: 仍为中文页面")

            except Exception as e:
                print(f"❌ 策略1异常: {e}")

            # 策略2: 访问美国首页建立英文会话
            try:
                print("🔄 策略2: 访问美国首页建立英文会话...")

                # 访问美国首页并设置地理位置
                base_urls = [
                    "https://www.amazon.com/?language=en_US&country=US&zipcode=10001",
                    "https://www.amazon.com/gp/navigation-country/select-country?ie=UTF8&language=en_US&country=US",
                    "https://www.amazon.com/customer-preferences/edit?ie=UTF8&preferencesReturnUrl=%2F&ref_=footer_lang"
                ]

                headers = self.get_us_headers()
                proxy = self.get_available_proxy()

                for base_url in base_urls:
                    try:
                        base_response = self.session.get(base_url, headers=headers, proxies=proxy, timeout=10)
                        if base_response.status_code == 200:
                            # 更新Cookie
                            self.set_us_location_cookies()
                            time.sleep(1)
                            break
                    except:
                        continue

                # 重新请求原URL
                final_response = self.session.get(new_url, headers=headers, proxies=proxy, timeout=12)

                if final_response.status_code == 200:
                    final_soup = BeautifulSoup(final_response.content, 'html.parser')
                    final_title = final_soup.find('title')

                    if final_title and not self.contains_chinese(final_title.text):
                        print("✅ 策略2成功: 重新建立会话后获取英文页面")
                        return final_response
                    else:
                        print("❌ 策略2失败: 重新建立会话后仍为中文")

            except Exception as e:
                print(f"❌ 策略2异常: {e}")

            # 策略3: 使用Cookie池中的其他Cookie
            try:
                print("🔄 策略3: 尝试Cookie池中的其他Cookie...")

                for i, alt_cookie in enumerate(self.cookie_pool):
                    if len(self.cookie_pool) <= 1:
                        break

                    print(f"   尝试Cookie {i+1}/{len(self.cookie_pool)}")

                    # 应用新Cookie
                    for name, value in alt_cookie['cookies'].items():
                        self.session.cookies.set(name, value, domain='.amazon.com', path='/')

                    # 强化地理位置设置
                    self.set_us_location_cookies()

                    # 发起请求
                    headers = self.get_us_headers()
                    proxy = self.get_available_proxy()
                    alt_response = self.session.get(new_url, headers=headers, proxies=proxy, timeout=10)

                    if alt_response.status_code == 200:
                        alt_soup = BeautifulSoup(alt_response.text, 'html.parser')
                        alt_title = alt_soup.find('title')

                        if alt_title and not self.contains_chinese(alt_title.text):
                            print(f"✅ 策略3成功: 使用Cookie {i+1} 获取英文页面")
                            self.update_cookie_stats(alt_cookie, success=True)
                            return alt_response

                    time.sleep(0.5)  # 短暂延迟

                print("❌ 策略3失败: 所有Cookie都无法获取英文页面")

            except Exception as e:
                print(f"❌ 策略3异常: {e}")

            print("❌ 所有增强策略失败，需要刷新Cookie池")

        return None

    # ===== 核心产品信息获取方法 =====

    def get_amazon_product_info(self, asin):
        """
        获取Amazon产品信息 - 增强反检测版本
        :param asin: Amazon产品的ASIN码
        :return: 包含产品信息的字典
        """
        # 检查是否需要刷新会话
        if self.should_refresh_session():
            self.refresh_session()

        # 优化URL参数，确保使用英文页面和美国区域
        url = f"https://www.amazon.com/dp/{asin}?language=en_US&zipcode=10001&LanguagePreference=en_US&country=US&ie=UTF8&ref_=nav_ya_signin&lc=en_US"
        retries = 5

        # 初始化结果字典
        result = {
            "asin": asin,
            "url": url,
            "title": None,
            "brand": None,
            "brand_type": None,
            "dimensions": None,
            "weight": None,
            "rating": None,
            "rating_count": None,
            "sales_rank": [],
            "image_url": None,
            "has_variants": False,
            "is_out_of_stock": False,
            "is_unique_brand": False,
            "html_content": ""
        }

        # 尝试多次使用随机代理和Cookie池
        for attempt in range(retries):
            # 获取最佳Cookie
            cookie_data = self.get_best_cookie()
            if cookie_data:
                # 应用Cookie到会话
                for name, value in cookie_data['cookies'].items():
                    self.session.cookies.set(name, value, domain='.amazon.com', path='/')
                print(f"🍪 使用Cookie池中的Cookie (成功率: {cookie_data['success_count']}/{cookie_data['success_count'] + cookie_data['failure_count']})")

            # 获取可用代理
            proxy = self.get_available_proxy()
            proxy_info = self.get_proxy_info(proxy)

            # 计算自适应延迟
            delay = self.calculate_adaptive_delay()
            print(f"ASIN {asin} 智能延迟: {delay:.2f}秒 (成功连击: {self.success_streak}, 失败连击: {self.failure_streak})")
            time.sleep(delay)

            print(f"ASIN {asin} 尝试 #{attempt + 1}/{retries} 使用代理: {proxy_info}")

            try:
                # 强化地理位置和语言设置 - 确保美国英文页面
                self.set_us_location_cookies()

                # 使用美国地区请求头
                headers = self.get_us_headers()

                # 增加请求计数
                self.request_count += 1

                # 执行请求
                response = self.session.get(url, headers=headers, proxies=proxy, timeout=15)

                # 处理各种HTTP状态码
                if response.status_code == 200:
                    # 更新Cookie统计（成功请求）
                    if cookie_data:
                        self.update_cookie_stats(cookie_data, success=True)

                    if "Click the button below to continue shopping" in response.text:
                        print(f"ASIN {asin} 遇到点击继续购物验证码，将代理放入冷静期")
                        # 更新Cookie统计（失败）
                        if cookie_data:
                            self.update_cookie_stats(cookie_data, success=False)

                        # 暂停当前代理
                        if proxy:
                            proxy_str = self.get_proxy_info(proxy)
                            self.suspended_proxies[proxy_str] = time.time()
                            print(f"已将代理放入冷静期，尝试使用新代理")
                        continue

                    elif "Type the characters you see in this image" in response.text or "captcha" in response.text.lower():
                        print(f"ASIN {asin} 遇到验证码，刷新Cookie池")

                        # 更新Cookie统计（遇到验证码视为部分失败）
                        if cookie_data:
                            self.update_cookie_stats(cookie_data, success=False)

                        # 刷新Cookie池
                        self.refresh_cookie_pool()
                        continue

                    # 检查是否需要登录或Cookie过期
                    elif "Sign in for the best experience" in response.text or "To discuss automated access to Amazon data please contact" in response.text:
                        print(f"ASIN {asin} Cookie可能已过期，刷新Cookie池")

                        # 更新Cookie统计（失败）
                        if cookie_data:
                            self.update_cookie_stats(cookie_data, success=False)

                        # 刷新Cookie池
                        self.refresh_cookie_pool()
                        continue

                    # 保存HTML内容以便后续分析
                    result["html_content"] = response.text

                    # 解析HTML
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 改进的语言检测逻辑，检查中文字符
                    title = soup.find('title')
                    if title:
                        title_text = title.text

                        if self.contains_chinese(title_text):
                            print(f"ASIN {asin}: 获取到中文页面，使用智能转换策略")

                            # 更新Cookie统计（中文页面视为失败）
                            if cookie_data:
                                self.update_cookie_stats(cookie_data, success=False)

                            # 智能转换策略：优先使用Cookie池，减少Selenium使用
                            conversion_success = False

                            # 策略1: 尝试使用现有的force_english_page方法
                            english_response = self.force_english_page(response)
                            if english_response:
                                response = english_response
                                soup = BeautifulSoup(response.content, 'html.parser')
                                new_title = soup.find('title')
                                if new_title and not self.contains_chinese(new_title.text):
                                    print(f"ASIN {asin}: 策略1成功 - 强制转换为英文页面")
                                    conversion_success = True

                            # 策略2: 如果策略1失败，尝试使用Cookie池中的其他Cookie
                            if not conversion_success and len(self.cookie_pool) > 1:
                                print(f"ASIN {asin}: 策略1失败，尝试Cookie池中的其他Cookie")
                                for i, alt_cookie in enumerate(self.cookie_pool):
                                    if alt_cookie != cookie_data:  # 跳过当前Cookie
                                        # 应用新Cookie
                                        for name, value in alt_cookie['cookies'].items():
                                            self.session.cookies.set(name, value, domain='.amazon.com', path='/')

                                        try:
                                            alt_response = self.session.get(url, headers=self.session.headers, proxies=proxy, timeout=10)
                                            if alt_response.status_code == 200:
                                                alt_soup = BeautifulSoup(alt_response.text, 'html.parser')
                                                alt_title = alt_soup.find('title')
                                                if alt_title and not self.contains_chinese(alt_title.text):
                                                    print(f"ASIN {asin}: 策略2成功 - 使用备用Cookie获取英文页面")
                                                    response = alt_response
                                                    soup = alt_soup
                                                    cookie_data = alt_cookie  # 更新当前使用的Cookie
                                                    self.update_cookie_stats(cookie_data, success=True)
                                                    conversion_success = True
                                                    break
                                        except Exception as e:
                                            print(f"尝试备用Cookie {i+1} 失败: {str(e)}")
                                            continue

                            # 策略3: 最后才使用Selenium刷新Cookie池
                            if not conversion_success:
                                print(f"ASIN {asin}: 策略2失败，最后使用Selenium刷新Cookie池")
                                self.refresh_cookie_pool()
                                continue  # 使用新Cookie重试
                        else:
                            print(f"ASIN {asin}: 确认页面为英文")

                    # 成功获取英文页面，开始解析产品信息
                    print(f"ASIN {asin}: 开始解析产品信息...")

                    # 解析产品标题
                    title_element = soup.find('span', {'id': 'productTitle'})
                    if title_element:
                        result["title"] = title_element.text.strip()
                        print(f"标题: {result['title'][:50]}...")

                    # 解析品牌信息
                    brand_element = soup.find('a', {'id': 'bylineInfo'}) or soup.find('span', class_='a-size-base-plus')
                    if brand_element:
                        brand_text = brand_element.text.strip()
                        if brand_text.startswith('Visit the '):
                            result["brand"] = brand_text.replace('Visit the ', '').replace(' Store', '')
                            result["brand_type"] = "visit_the_brand"
                        elif brand_text.startswith('Brand: '):
                            result["brand"] = brand_text.replace('Brand: ', '')
                            result["brand_type"] = "brand_label"
                        else:
                            result["brand"] = brand_text
                            result["brand_type"] = "other"

                        print(f"品牌: {result['brand']} (类型: {result['brand_type']})")

                    # 成功解析，返回结果
                    self.success_count += 1
                    return result

                else:
                    print(f"ASIN {asin} HTTP状态码: {response.status_code}")
                    if cookie_data:
                        self.update_cookie_stats(cookie_data, success=False)

                    if response.status_code in [503, 429]:
                        # 服务器过载，增加延迟
                        time.sleep(random.uniform(5, 10))

                    continue

            except Exception as e:
                print(f"ASIN {asin} 请求异常: {str(e)}")
                if cookie_data:
                    self.update_cookie_stats(cookie_data, success=False)

                # 网络错误，短暂延迟后重试
                time.sleep(random.uniform(2, 5))
                continue

        # 所有重试都失败
        print(f"ASIN {asin} 所有重试都失败")
        self.error_count += 1
        return result

if __name__ == "__main__":
    print("🎯 Amazon产品筛选器 - 增强反检测版本")
    print("=" * 50)

    scraper = EnhancedAmazonScraper()
    print("系统初始化完成，准备开始工作...")

    # 测试单个ASIN
    test_asin = "B08N5WRWNW"  # 示例ASIN
    print(f"\n🧪 测试ASIN: {test_asin}")
    result = scraper.get_amazon_product_info(test_asin)

    if result["title"]:
        print(f"✅ 成功获取产品信息:")
        print(f"   标题: {result['title']}")
        print(f"   品牌: {result['brand']}")
        print(f"   品牌类型: {result['brand_type']}")
    else:
        print("❌ 未能获取产品信息")

    print(f"\n📊 统计信息:")
    print(f"   成功: {scraper.success_count}")
    print(f"   失败: {scraper.error_count}")
    print(f"   Cookie池大小: {len(scraper.cookie_pool)}")
    print(f"   请求计数: {scraper.request_count}")
