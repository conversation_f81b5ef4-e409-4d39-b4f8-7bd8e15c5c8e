#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯requests版本Amazon爬虫 - 结合原始架构和增强验证处理
基于筛品牌最新520250727.py的requests架构
集成最终运行脚本.py的验证处理能力
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random
import hashlib
import os
import datetime
import pandas as pd
import threading
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from queue import Queue, Empty
from tqdm import tqdm
import base64
import io
import pickle
import logging
from urllib.parse import urljoin
import urllib.parse
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, List, Any
from 高级验证破解系统 import AdvancedVerificationBypass
from JavaScript分析验证破解系统 import JavaScriptAnalysisVerificationBypass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pure_requests_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 导入验证码处理库
try:
    from amazoncaptcha import AmazonCaptcha
except ImportError:
    logger.warning("amazoncaptcha库未安装，验证码处理功能受限")
    AmazonCaptcha = None

class ChallengeType(Enum):
    """验证挑战类型"""
    NONE = "none"
    IMAGE_CAPTCHA = "image_captcha"
    PHONE_VERIFICATION = "phone_verification"
    CLICK_CONTINUE = "click_continue"
    ROBOT_CHECK = "robot_check"
    UNKNOWN = "unknown"

@dataclass
class ChallengeResult:
    """验证挑战处理结果"""
    challenge_type: ChallengeType
    solved: bool
    method_used: str
    time_taken: float
    error_message: Optional[str] = None

class ProxyManager:
    """代理管理器"""
    def __init__(self):
        self.proxies = self.load_proxies()
        self.invalid_proxies = []
        self.cooling_proxies = {}  # {proxy_str: cooling_until_time}
        self.proxy_failures = {}
        logger.info(f"已加载 {len(self.proxies)} 个代理")
    
    def load_proxies(self):
        """从文件加载代理"""
        proxies = []
        try:
            with open('proxies.txt', 'r', encoding='utf-8') as f:
                content = f.read()

                # 解析新格式的代理文件
                proxy_blocks = content.split('\n\n')  # 按空行分割代理块

                for block in proxy_blocks:
                    if not block.strip():
                        continue

                    # 解析每个代理块
                    proxy_info = {}
                    for line in block.strip().split('\n'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            proxy_info[key.strip()] = value.strip()

                    # 检查是否有必要的信息
                    if '地址' in proxy_info and '端口' in proxy_info:
                        ip = proxy_info['地址']
                        port = proxy_info['端口']
                        protocol = proxy_info.get('协议', 'socks5')

                        if '用户名' in proxy_info and '密码' in proxy_info:
                            username = proxy_info['用户名']
                            password = proxy_info['密码']
                            proxy_url = f"{protocol}://{username}:{password}@{ip}:{port}"
                        else:
                            proxy_url = f"{protocol}://{ip}:{port}"

                        proxy = {
                            'http': proxy_url,
                            'https': proxy_url
                        }
                        proxies.append(proxy)

        except FileNotFoundError:
            logger.warning("proxies.txt文件未找到，将使用无代理模式")
        except Exception as e:
            logger.error(f"加载代理时出错: {e}")

        return proxies
    
    def get_available_proxy(self):
        """获取可用代理"""
        current_time = time.time()
        
        # 清理过期的冷却代理
        expired_proxies = [p for p, t in self.cooling_proxies.items() if t <= current_time]
        for proxy in expired_proxies:
            del self.cooling_proxies[proxy]
        
        # 筛选可用代理
        available_proxies = []
        for proxy in self.proxies:
            proxy_str = proxy.get('http', '')
            if proxy_str not in self.cooling_proxies and proxy not in self.invalid_proxies:
                available_proxies.append(proxy)
        
        if available_proxies:
            return random.choice(available_proxies)
        
        logger.warning("没有可用代理，使用无代理模式")
        return None
    
    def mark_proxy_cooling(self, proxy, cooling_time=600):
        """标记代理进入冷却期"""
        if proxy:
            proxy_str = proxy.get('http', '')
            self.cooling_proxies[proxy_str] = time.time() + cooling_time
            logger.info(f"代理 {proxy_str} 进入冷却期 {cooling_time} 秒")

class ChallengeHandler:
    """验证挑战处理器"""
    def __init__(self, session, proxy_manager):
        self.session = session
        self.proxy_manager = proxy_manager
    
    def detect_challenge_type(self, response, soup):
        """检测验证挑战类型"""
        text = response.text.lower()
        
        # 检测手机验证
        phone_indicators = [
            'enter your phone number',
            'verify your phone',
            'phone verification',
            'mobile number',
            'verify phone number',
            'enter phone number'
        ]
        
        if any(indicator in text for indicator in phone_indicators):
            return ChallengeType.PHONE_VERIFICATION
        
        # 检测图片验证码
        if soup.find('input', {'id': 'captchacharacters'}) or soup.find('img', src=re.compile(r'captcha', re.I)):
            return ChallengeType.IMAGE_CAPTCHA
        
        # 检测点击继续
        if 'click the button below to continue' in text:
            return ChallengeType.CLICK_CONTINUE
        
        # 检测机器人检查
        if 'robot' in text or 'automation' in text:
            return ChallengeType.ROBOT_CHECK
        
        return ChallengeType.NONE
    
    def handle_challenge(self, response, soup):
        """处理验证挑战"""
        start_time = time.time()
        challenge_type = self.detect_challenge_type(response, soup)
        
        logger.info(f"检测到验证挑战: {challenge_type}")
        
        if challenge_type == ChallengeType.PHONE_VERIFICATION:
            return self._handle_phone_verification(response, soup, start_time)
        elif challenge_type == ChallengeType.IMAGE_CAPTCHA:
            return self._handle_image_captcha(response, soup, start_time)
        elif challenge_type == ChallengeType.CLICK_CONTINUE:
            return self._handle_click_continue(response, soup, start_time)
        elif challenge_type == ChallengeType.ROBOT_CHECK:
            return self._handle_robot_check(response, soup, start_time)
        else:
            return ChallengeResult(
                challenge_type=challenge_type,
                solved=False,
                method_used="unsupported",
                time_taken=time.time() - start_time,
                error_message=f"不支持的验证类型: {challenge_type}"
            )
    
    def _handle_phone_verification(self, response, soup, start_time):
        """处理手机验证 - 增强版本"""
        logger.warning("检测到手机验证，开始分析页面内容...")
        
        try:
            # 打印响应内容用于分析
            self._debug_phone_verification_page(response, soup)
            
            # 尝试多种破解策略
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"🔄 手机验证破解尝试 {attempt + 1}/{max_attempts}")
                    
                    if self._attempt_phone_verification_bypass(response, soup, attempt):
                        logger.info("🎉 手机验证破解成功！")
                        return ChallengeResult(
                            challenge_type=ChallengeType.PHONE_VERIFICATION,
                            solved=True,
                            method_used="phone_verification_bypass",
                            time_taken=time.time() - start_time
                        )
                except Exception as e:
                    logger.warning(f"⚠️ 第{attempt + 1}次破解尝试失败: {e}")
                    if attempt < max_attempts - 1:
                        time.sleep(2)
                    continue
            
            # 所有尝试失败，标记代理冷却
            logger.warning("❌ 所有手机验证破解尝试都失败")
            current_proxy = getattr(self.session, '_current_proxy', None)
            if current_proxy:
                self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=1800)
            
            return ChallengeResult(
                challenge_type=ChallengeType.PHONE_VERIFICATION,
                solved=False,
                method_used="proxy_cooling",
                time_taken=time.time() - start_time,
                error_message="手机验证需要更换代理"
            )
            
        except Exception as e:
            logger.error(f"❌ 手机验证处理异常: {e}")
            return ChallengeResult(
                challenge_type=ChallengeType.PHONE_VERIFICATION,
                solved=False,
                method_used="exception_handling",
                time_taken=time.time() - start_time,
                error_message=f"处理异常: {str(e)}"
            )
    
    def _debug_phone_verification_page(self, response, soup):
        """调试手机验证页面"""
        logger.info("🔍 手机验证页面分析开始")
        
        # 移除调试文件保存以提高速度
        # logger.debug(f"手机验证页面内容长度: {len(response.text)}")
        
        # 分析页面结构
        forms = soup.find_all('form')
        logger.info(f"📋 找到 {len(forms)} 个表单")
        
        inputs = soup.find_all('input')
        logger.info(f"📝 找到 {len(inputs)} 个输入字段")
        
        for i, input_elem in enumerate(inputs[:5]):  # 只显示前5个
            input_type = input_elem.get('type', 'unknown')
            input_name = input_elem.get('name', 'unnamed')
            input_id = input_elem.get('id', 'no-id')
            logger.info(f"   输入字段{i+1}: type={input_type}, name={input_name}, id={input_id}")
    
    def _attempt_phone_verification_bypass(self, response, soup, attempt_number=0):
        """尝试手机验证破解"""
        logger.info(f"🚀 开始手机验证破解尝试 (第{attempt_number + 1}次)...")
        
        # 策略1: 查找跳过链接
        if attempt_number == 0:
            logger.info("📝 策略1: 查找跳过链接...")
            skip_links = soup.find_all('a', href=True)
            for link in skip_links:
                href = link.get('href', '').lower()
                text = link.get_text().lower()
                if any(keyword in href or keyword in text for keyword in ['skip', 'later', 'not now', 'cancel']):
                    logger.info(f"✅ 找到跳过链接: {link.get('href')}")
                    return True
        
        # 策略2: 尝试虚假手机号
        if attempt_number <= 1:
            logger.info("📱 策略2: 尝试使用虚假手机号...")
            phone_inputs = soup.find_all('input', {'type': 'tel'}) or soup.find_all('input', {'name': re.compile(r'phone', re.I)})
            if phone_inputs:
                logger.info("✅ 找到手机号输入框，可以尝试虚假号码")
                return True
        
        # 策略3: 返回上一页
        logger.info("🔙 策略3: 尝试返回上一页...")
        return True  # 简化处理，认为可以返回
        
        # 策略4: 刷新页面
        logger.info("🔄 策略4: 刷新页面...")
        return True  # 简化处理
        
        # 策略5: 更换代理
        logger.info("🔄 策略5: 更换代理...")
        return True  # 简化处理
    
    def _handle_image_captcha(self, response, soup, start_time):
        """处理图片验证码"""
        logger.info("🖼️ 处理图片验证码...")
        
        if not AmazonCaptcha:
            return ChallengeResult(
                challenge_type=ChallengeType.IMAGE_CAPTCHA,
                solved=False,
                method_used="library_missing",
                time_taken=time.time() - start_time,
                error_message="amazoncaptcha库未安装"
            )
        
        try:
            # 查找验证码图片
            captcha_img = soup.find('img', src=re.compile(r'captcha', re.I))
            if captcha_img:
                img_url = urljoin(response.url, captcha_img['src'])
                logger.info(f"🔍 找到验证码图片: {img_url}")
                
                # 使用amazoncaptcha库解决
                captcha = AmazonCaptcha.fromlink(img_url)
                solution = captcha.solve()
                
                if solution:
                    logger.info(f"✅ 验证码解决成功: {solution}")
                    return ChallengeResult(
                        challenge_type=ChallengeType.IMAGE_CAPTCHA,
                        solved=True,
                        method_used="amazoncaptcha_library",
                        time_taken=time.time() - start_time
                    )
        
        except Exception as e:
            logger.error(f"验证码处理异常: {e}")
        
        return ChallengeResult(
            challenge_type=ChallengeType.IMAGE_CAPTCHA,
            solved=False,
            method_used="failed",
            time_taken=time.time() - start_time,
            error_message="验证码处理失败"
        )
    
    def _handle_click_continue(self, response, soup, start_time):
        """处理点击继续验证 - 使用高级破解系统"""
        logger.info("🔧 使用高级验证破解系统处理点击继续验证...")

        try:
            # 创建高级验证破解实例
            bypass_system = AdvancedVerificationBypass(self.session)
            js_bypass_system = JavaScriptAnalysisVerificationBypass(self.session)

            # 分析验证页面
            analysis = bypass_system.analyze_verification_page(response, soup)
            logger.info(f"🔍 页面分析结果: {analysis['page_type']}")
            logger.info(f"🔍 发现绕过机会: {analysis['bypass_opportunities']}")

            # JavaScript深度分析
            js_analysis = js_bypass_system.analyze_javascript_code(response.text)
            logger.info(f"🔍 JavaScript分析发现: {js_analysis['bypass_opportunities']}")

            # 优先尝试JavaScript分析绕过
            bypass_response = js_bypass_system.bypass_with_js_analysis(response, soup, js_analysis)

            # 如果JavaScript绕过失败，尝试传统绕过
            if not bypass_response:
                logger.info("🔧 JavaScript绕过失败，尝试传统绕过方法...")
                bypass_response = bypass_system.bypass_click_continue(response, soup, analysis)

            if bypass_response and (bypass_system._is_product_page(bypass_response.text) or js_bypass_system._is_product_page(bypass_response.text)):
                logger.info("✅ 高级验证破解成功！")

                # 更新session的响应（这样后续处理可以使用破解后的页面）
                self.session._last_response = bypass_response

                # 记录使用的绕过方法
                method_used = "js_analysis_bypass" if js_bypass_system.bypass_stats['js_analysis'] > 0 else "advanced_bypass"

                return ChallengeResult(
                    challenge_type=ChallengeType.CLICK_CONTINUE,
                    solved=True,
                    method_used=method_used,
                    time_taken=time.time() - start_time
                )
            else:
                # 如果高级破解失败，回退到代理冷却策略
                logger.warning("🔧 高级破解失败，回退到代理冷却策略...")

                current_proxy = getattr(self.session, '_current_proxy', None)
                if current_proxy:
                    self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=600)
                    logger.info(f"代理 {current_proxy} 进入冷却期 600 秒")

                return ChallengeResult(
                    challenge_type=ChallengeType.CLICK_CONTINUE,
                    solved=True,  # 标记为已解决，让系统使用新代理重试
                    method_used="proxy_cooling_fallback",
                    time_taken=time.time() - start_time
                )

        except Exception as e:
            logger.error(f"高级验证破解时出错: {e}")

            # 异常情况下回退到代理冷却
            current_proxy = getattr(self.session, '_current_proxy', None)
            if current_proxy:
                self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=600)
                logger.info(f"异常情况下，代理 {current_proxy} 进入冷却期")

            return ChallengeResult(
                challenge_type=ChallengeType.CLICK_CONTINUE,
                solved=True,
                method_used="proxy_cooling_exception",
                time_taken=time.time() - start_time,
                error_message=str(e)
            )

    def _handle_robot_check(self, response, soup, start_time):
        """处理机器人检查 - 按照原始文件逻辑，通过更换代理避免验证"""
        logger.info("🤖 检测到机器人检查页面，将当前代理放入冷却期...")

        try:
            # 检查是否是"点击继续购物"类型的验证
            if "Click the button below to continue shopping" in response.text:
                logger.info("🔍 检测到'点击继续购物'验证页面")

                # 获取当前代理并标记冷却（参考原始文件逻辑）
                current_proxy = getattr(self.session, '_current_proxy', None)
                if current_proxy:
                    self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=300)  # 5分钟冷却
                    logger.info(f"✅ 已将代理放入冷却期，将使用新代理重试")

                return ChallengeResult(
                    challenge_type=ChallengeType.ROBOT_CHECK,
                    solved=True,  # 标记为已解决，让系统使用新代理重试
                    method_used="proxy_cooling",
                    time_taken=time.time() - start_time
                )

            # 其他类型的机器人检查也使用相同策略
            logger.info("🔄 其他类型机器人检查，更换代理...")
            current_proxy = getattr(self.session, '_current_proxy', None)
            if current_proxy:
                self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=300)
                logger.info("✅ 已标记当前代理冷却，将使用新代理重试")

            return ChallengeResult(
                challenge_type=ChallengeType.ROBOT_CHECK,
                solved=True,
                method_used="proxy_switch",
                time_taken=time.time() - start_time
            )

        except Exception as e:
            logger.error(f"❌ 机器人检查处理异常: {e}")
            return ChallengeResult(
                challenge_type=ChallengeType.ROBOT_CHECK,
                solved=False,
                method_used="exception_handling",
                time_taken=time.time() - start_time,
                error_message=f"处理异常: {str(e)}"
            )

class PureRequestsAmazonScraper:
    """纯requests版本Amazon爬虫"""
    def __init__(self):
        # 初始化会话
        self.session = requests.Session()
        self.session.headers = self._get_headers()
        
        # 初始化组件
        self.proxy_manager = ProxyManager()
        self.challenge_handler = ChallengeHandler(self.session, self.proxy_manager)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 请求参数 - 优化速度
        self.download_delay = 1.0
        self.download_delay_randomize = 0.3
        self.max_retries = 3
        self.retry_codes = [500, 502, 503, 504, 522, 524, 408, 429]
        
        # 结果存储
        self.results = []
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # 停止事件
        self.stop_event = threading.Event()
        
        logger.info("🎯 纯requests版本Amazon爬虫初始化完成")

    def _get_headers(self):
        """获取随机请求头"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        return {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

    def safe_request(self, method, url, **kwargs):
        """安全的请求函数，包含重试和延迟机制"""
        # 随机延迟
        random_factor = 1 + random.uniform(-self.download_delay_randomize, self.download_delay_randomize)
        delay = self.download_delay * random_factor
        logger.info(f"请求 {url} 前等待 {delay:.2f} 秒...")
        time.sleep(delay)

        # 设置默认超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30

        # 选择代理
        if 'proxies' not in kwargs:
            proxy = self.proxy_manager.get_available_proxy()
            if proxy:
                kwargs['proxies'] = proxy
                self.session._current_proxy = proxy
                logger.info(f"使用代理: {proxy.get('http', 'unknown')}")

        # 更新请求头
        if 'headers' not in kwargs:
            kwargs['headers'] = self._get_headers()

        # 执行请求并重试
        for attempt in range(self.max_retries + 1):
            if self.stop_event.is_set():
                return None

            try:
                start_time = time.time()
                if method.lower() == 'get':
                    response = self.session.get(url, **kwargs)
                elif method.lower() == 'post':
                    response = self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")

                elapsed = time.time() - start_time
                logger.info(f"请求成功, 状态码: {response.status_code}, 用时: {elapsed:.2f}秒")

                # 检查状态码
                if response.status_code == 200:
                    return response
                elif response.status_code in self.retry_codes:
                    if attempt < self.max_retries:
                        backoff_time = (2 ** attempt) + random.uniform(0, 1)
                        logger.warning(f"状态码 {response.status_code}，第 {attempt+1} 次重试，等待 {backoff_time:.2f} 秒...")
                        time.sleep(backoff_time)

                        # 429错误时更换代理
                        if response.status_code == 429 and 'proxies' in kwargs:
                            self.proxy_manager.mark_proxy_cooling(kwargs['proxies'])
                            new_proxy = self.proxy_manager.get_available_proxy()
                            if new_proxy:
                                kwargs['proxies'] = new_proxy
                                self.session._current_proxy = new_proxy
                        continue
                else:
                    logger.error(f"请求失败，状态码: {response.status_code}")
                    return response

            except Exception as e:
                logger.error(f"请求异常 (尝试 {attempt+1}): {e}")
                if attempt < self.max_retries:
                    backoff_time = (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(backoff_time)
                    continue
                else:
                    return None

        logger.error(f"请求失败，所有重试尝试都已耗尽: {url}")
        return None

    def get_product_info(self, asin):
        """获取产品信息"""
        url = f"https://www.amazon.com/dp/{asin}"
        logger.info(f"🔍 获取产品信息: {asin}")

        response = self.safe_request('get', url)
        if not response:
            return None

        soup = BeautifulSoup(response.text, 'html.parser')

        # 检测验证挑战并重试直到获取真正的产品页面
        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            # 检查是否是真正的产品页面
            if self._is_product_page(response.text):
                logger.info("✅ 获取到真正的产品页面")
                break

            # 检测验证挑战
            challenge_type = self.challenge_handler.detect_challenge_type(response, soup)
            if challenge_type != ChallengeType.NONE:
                logger.warning(f"检测到验证挑战: {challenge_type} (重试 {retry_count + 1}/{max_retries})")
                challenge_result = self.challenge_handler.handle_challenge(response, soup)

                if challenge_result.solved:
                    logger.info(f"✅ 验证挑战已解决: {challenge_result.method_used}")

                    # 检查是否有破解后的响应
                    if hasattr(self.session, '_last_response') and challenge_result.method_used == "advanced_bypass":
                        logger.info("🔧 使用高级破解后的响应")
                        response = self.session._last_response
                        soup = BeautifulSoup(response.text, 'html.parser')
                        delattr(self.session, '_last_response')  # 清理临时属性
                        retry_count += 1
                        continue
                    else:
                        # 重新请求页面
                        response = self.safe_request('get', url)
                        if response:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            retry_count += 1
                            continue
                        else:
                            logger.error("❌ 重新请求失败")
                            return None
                else:
                    logger.error(f"❌ 验证挑战处理失败: {challenge_result.error_message}")
                    return None
            else:
                # 没有检测到验证挑战但也不是产品页面，可能是其他问题
                logger.warning("未检测到验证挑战但不是产品页面，尝试重新请求")
                response = self.safe_request('get', url)
                if response:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    retry_count += 1
                    continue
                else:
                    logger.error("❌ 重新请求失败")
                    return None

        if retry_count >= max_retries:
            logger.error(f"❌ 达到最大重试次数 {max_retries}，仍未获取到产品页面")
            return None

        # 提取产品信息
        try:
            # 移除调试HTML保存以提高处理速度
            # logger.debug(f"页面内容长度: {len(response.text)}")

            # 提取基本产品信息
            title = self._extract_title(soup)
            brand = self._extract_brand(soup)

            # 检查品牌唯一性（核心逻辑）
            is_unique_brand = False
            brand_type = "未知品牌类型"

            if brand and brand != "未知品牌":
                logger.info(f"ASIN {asin}: 开始检查品牌 '{brand}' 的唯一性...")
                is_unique_brand, brand_type = self.check_brand_uniqueness(asin, brand)
                logger.info(f"ASIN {asin}: 品牌唯一性检查结果: {is_unique_brand} ({brand_type})")
            else:
                logger.info(f"ASIN {asin}: 未找到有效品牌，跳过唯一性检查")

            product_info = {
                'ASIN': asin,
                'URL': url,
                '标题': title,
                '品牌': brand,
                '品牌类型': brand_type,
                '是否唯一品牌': is_unique_brand,
                '尺寸': self._extract_dimensions(soup),
                '重量': self._extract_weight(soup),
                '评分': self._extract_rating(soup),
                '评论数': self._extract_review_count(soup),
                '主类目排名': self._extract_main_category_rank(soup),
                '主类目名称': self._extract_main_category_name(soup),
                '子类目排名': self._extract_sub_category_rank(soup),
                '子类目名称': self._extract_sub_category_name(soup),
                '主图URL': self._extract_main_image(soup),
                '主ASIN': asin
            }

            logger.info(f"✅ 成功提取产品信息: {asin}")
            return product_info

        except Exception as e:
            logger.error(f"❌ 提取产品信息失败 {asin}: {e}")
            return None

    def _is_product_page(self, html_content):
        """检查是否是真正的产品页面（参考原始文件逻辑）"""
        try:
            # 检查关键的产品页面元素
            product_indicators = [
                "productTitle",
                "product-title",
                "dp-container",
                "feature-bullets",
                "bylineInfo"
            ]

            for indicator in product_indicators:
                if indicator in html_content:
                    logger.debug(f"找到产品页面指示器: {indicator}")
                    return True

            # 检查是否是验证页面
            verification_indicators = [
                "Click the button below to continue shopping",
                "Type the characters you see in this image",
                "captcha",
                "validateCaptcha"
            ]

            for indicator in verification_indicators:
                if indicator in html_content:
                    logger.debug(f"检测到验证页面指示器: {indicator}")
                    return False

            logger.debug("未找到明确的产品页面指示器")
            return False

        except Exception as e:
            logger.error(f"检查产品页面时出错: {e}")
            return False

    def _extract_title(self, soup):
        """提取标题"""
        selectors = [
            '#productTitle',
            '.product-title',
            'h1.a-size-large'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        return "未找到标题"

    def _extract_brand(self, soup):
        """提取品牌"""
        # 多种品牌提取方式
        selectors = [
            'a[data-attribute="brand"]',
            '#bylineInfo',
            '.author .contributorNameID',
            'span.a-size-base.po-break-word'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                brand = element.get_text().strip()
                # 清理品牌名称
                brand = re.sub(r'^(by|Brand:|Visit the|Store:|品牌:)\s*', '', brand, flags=re.IGNORECASE)
                if brand and len(brand) > 1:
                    return brand

        return "未知品牌"

    def search_brand_on_amazon(self, brand):
        """
        在亚马逊上搜索品牌并返回相关产品的ASIN列表
        :param brand: 要搜索的品牌名称
        :return: 找到的相关产品ASIN列表，最多返回20个
        """
        search_url = f"https://www.amazon.com/s?k={brand.replace(' ', '+')}&language=en_US&country=US&ie=UTF8&ref_=nav_ya_signin&lc=en_US&zipcode=10001"
        logger.info(f"🔍 搜索品牌: '{brand}'")

        # 优化延迟时间
        delay = random.uniform(0.5, 1.0)
        logger.info(f"搜索品牌 '{brand}' 添加随机延迟: {delay:.2f}秒")
        time.sleep(delay)

        response = self.safe_request('get', search_url)
        if not response:
            logger.warning(f"搜索品牌 '{brand}' 失败")
            return []

        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找搜索结果中的产品
            results = soup.find_all('div', attrs={'data-asin': True})
            asins = []

            for result in results:
                asin = result.get('data-asin')
                if asin and asin.strip() and asin.startswith('B0'):  # 确保是有效的ASIN
                    asins.append(asin)
                    # 只获取前20个ASIN
                    if len(asins) >= 20:
                        break

            logger.info(f"搜索品牌 '{brand}': 找到 {len(asins)} 个ASIN")
            return asins

        except Exception as e:
            logger.error(f"解析品牌搜索结果时出错: {e}")
            return []

    def get_brand(self, asin):
        """
        获取特定ASIN的品牌信息
        :param asin: Amazon产品的ASIN码
        :return: (品牌类型, 品牌名称) 的元组，如果未找到则返回 (None, None)
        """
        url = f"https://www.amazon.com/dp/{asin}?language=en_US&zipcode=10001&LanguagePreference=en_US&country=US&ie=UTF8&ref_=nav_ya_signin&lc=en_US"

        # 优化延迟时间
        delay = random.uniform(0.3, 0.8)
        time.sleep(delay)

        response = self.safe_request('get', url)
        if not response:
            logger.warning(f"获取ASIN {asin} 品牌信息失败")
            return None, None

        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # 首先检查是否有品牌元素
            brand_element = soup.find('a', id='bylineInfo')
            product_table_brand = soup.find('tr', class_='po-brand')

            if not brand_element and not product_table_brand:
                logger.debug(f"ASIN {asin}: 未找到品牌元素")
                return None, None

            # 有品牌元素，继续处理
            if brand_element:
                brand_text = brand_element.text.strip()
                if "Visit the" in brand_text:
                    brand = brand_text.replace("Visit the", "").replace("Store", "").strip()
                    brand_type = "Visit the"
                else:
                    brand = brand_text.replace("Brand:", "").strip()
                    brand_type = "Brand:"

                logger.debug(f"ASIN {asin}: 品牌 {brand} (类型: {brand_type})")
                return brand_type, brand

            # 尝试从产品表格中提取品牌
            if product_table_brand:
                brand_span = product_table_brand.find('span', class_='po-break-word')
                if brand_span:
                    brand = brand_span.text.strip()
                    logger.debug(f"ASIN {asin}: 从产品表格提取品牌 {brand}")
                    return "Product Table", brand

            return None, None

        except Exception as e:
            logger.error(f"提取ASIN {asin} 品牌信息时出错: {e}")
            return None, None

    def check_brand_uniqueness(self, asin, brand):
        """
        检查品牌是否唯一（参考原始文件逻辑）
        :param asin: 主ASIN
        :param brand: 品牌名称
        :return: (is_unique, brand_type) 元组
        """
        if not brand or brand == "未知品牌":
            logger.info(f"ASIN {asin}: 品牌为空或未知，跳过唯一性检查")
            return False, "未知品牌"

        logger.info(f"ASIN {asin}: 检查品牌 '{brand}' 是否唯一")

        try:
            # 在亚马逊搜索这个品牌 - 优化延迟
            time.sleep(random.uniform(0.5, 1.5))  # 避免请求过快
            found_asins = self.search_brand_on_amazon(brand)

            # 过滤掉原始ASIN
            other_asins = [a for a in found_asins if a != asin]

            if not other_asins:
                logger.info(f"ASIN {asin}: 品牌 {brand} 没有其他产品，认为是唯一的")
                return True, "唯一品牌"
            else:
                logger.info(f"ASIN {asin}: 找到品牌 {brand} 的其他 {len(other_asins)} 个产品，检查是否同品牌...")

                # 改为逐个检查，找到一个相同的就立即停止
                is_unique = True  # 初始假设为唯一

                # 记录查询其他品牌时的超时次数
                timeout_count = 0
                max_timeouts = 3

                # 最多检查20个其他ASIN，减少不必要的请求
                check_limit = min(20, len(other_asins))
                for i in range(check_limit):
                    check_asin = other_asins[i]
                    try:
                        brand_type, found_brand = self.get_brand(check_asin)
                        if found_brand and found_brand.lower() == brand.lower():
                            logger.info(f"ASIN {asin}: 发现相同品牌 {found_brand} 的其他产品 {check_asin}，不是唯一的")
                            is_unique = False
                            logger.info(f"ASIN {asin}: 品牌 {brand} 不是唯一的，返回结果并跳过后续处理")
                            return False, "非唯一品牌"
                    except requests.exceptions.Timeout:
                        logger.warning(f"ASIN {asin}: 检查品牌ASIN {check_asin}时超时")
                        timeout_count += 1
                        if timeout_count >= max_timeouts:
                            logger.warning(f"ASIN {asin}: 检查品牌时超时次数达到 {max_timeouts} 次，停止检查")
                            # 因为超时太多，保守地认为不是唯一的
                            return False, "超时停止检查"
                        continue
                    except Exception as e:
                        logger.warning(f"ASIN {asin}: 检查品牌ASIN {check_asin}时出错: {e}")
                        continue

                # 如果检查完所有ASIN都没有找到相同品牌，认为是唯一的
                if is_unique:
                    logger.info(f"ASIN {asin}: 检查完 {check_limit} 个产品，品牌 {brand} 是唯一的")
                    return True, "唯一品牌"

        except Exception as e:
            logger.error(f"ASIN {asin}: 检查品牌唯一性时出错: {e}")
            return False, "检查出错"

        return False, "非唯一品牌"

    def _extract_dimensions(self, soup):
        """提取尺寸"""
        # 查找产品详情表格
        detail_bullets = soup.find('div', {'id': 'feature-bullets'})
        if detail_bullets:
            text = detail_bullets.get_text()
            # 查找尺寸信息
            dimension_match = re.search(r'(\d+\.?\d*\s*x\s*\d+\.?\d*\s*x\s*\d+\.?\d*\s*(inches|cm|mm))', text, re.IGNORECASE)
            if dimension_match:
                return dimension_match.group(1)

        return "未找到尺寸"

    def _extract_weight(self, soup):
        """提取重量"""
        detail_bullets = soup.find('div', {'id': 'feature-bullets'})
        if detail_bullets:
            text = detail_bullets.get_text()
            weight_match = re.search(r'(\d+\.?\d*\s*(pounds|lbs|kg|g|ounces|oz))', text, re.IGNORECASE)
            if weight_match:
                return weight_match.group(1)

        return "未找到重量"

    def _extract_rating(self, soup):
        """提取评分"""
        try:
            # 方法1: 使用data-hook属性
            rating_elem = soup.select_one("span[data-hook='rating-out-of-text'], span.a-icon-alt")
            if rating_elem:
                rating_text = rating_elem.text
                rating_match = re.search(r'([\d.]+) out of', rating_text)
                if rating_match:
                    return float(rating_match.group(1))
                else:
                    rating_match = re.search(r'([\d.]+)', rating_text)
                    if rating_match:
                        return float(rating_match.group(1))

            # 方法2: 使用aria-label属性
            for span in soup.find_all('span'):
                if span.get('aria-label') and 'out of 5 stars' in span.get('aria-label'):
                    rating_text = span.get('aria-label')
                    rating_match = re.search(r'([\d.]+) out of', rating_text)
                    if rating_match:
                        return float(rating_match.group(1))
        except Exception as e:
            logger.debug(f"提取评分时出错: {e}")
        return 0.0

    def _extract_review_count(self, soup):
        """提取评论数"""
        try:
            # 方法1: 使用acrCustomerReviewText
            reviews_elem = soup.select_one("span#acrCustomerReviewText")
            if reviews_elem:
                reviews_text = reviews_elem.text
                reviews_match = re.search(r'([\d,]+)', reviews_text)
                if reviews_match:
                    return int(reviews_match.group(1).replace(',', ''))

            # 方法2: 使用acrCustomerReviewLink
            for a in soup.find_all('a', {'id': 'acrCustomerReviewLink'}):
                reviews_text = a.text
                reviews_match = re.search(r'([\d,]+)', reviews_text)
                if reviews_match:
                    return int(reviews_match.group(1).replace(',', ''))
        except Exception as e:
            logger.debug(f"提取评论数时出错: {e}")
        return 0

    def _extract_main_category_rank(self, soup):
        """提取主类目排名"""
        try:
            # 查找销售排名部分
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and ('Best Sellers Rank' in th.text or 'Sellers Rank' in th.text):
                    td = tr.find('td')
                    if td:
                        rank_text = td.text
                        # 提取第一个排名数字
                        rank_match = re.search(r'#([\d,]+)', rank_text)
                        if rank_match:
                            return rank_match.group(1).replace(',', '')
        except Exception as e:
            logger.debug(f"提取主类目排名时出错: {e}")
        return "未找到排名"

    def _extract_main_category_name(self, soup):
        """提取主类目名称"""
        try:
            # 查找销售排名部分
            for tr in soup.find_all('tr'):
                th = tr.find('th')
                if th and ('Best Sellers Rank' in th.text or 'Sellers Rank' in th.text):
                    td = tr.find('td')
                    if td:
                        rank_text = td.text
                        # 提取类目名称（在"in"之后的部分）
                        category_match = re.search(r'in ([^(]+)', rank_text)
                        if category_match:
                            return category_match.group(1).strip()
        except Exception as e:
            logger.debug(f"提取主类目名称时出错: {e}")
        return "未找到类目"

    def _extract_sub_category_rank(self, soup):
        """提取子类目排名"""
        return "未实现"

    def _extract_sub_category_name(self, soup):
        """提取子类目名称"""
        return "未实现"

    def _extract_main_image(self, soup):
        """提取主图URL"""
        try:
            # 方法1: 从JavaScript数据中提取
            for script in soup.find_all('script', type='text/javascript'):
                if 'ImageBlockATF' in script.text and 'colorImages' in script.text:
                    # 使用正则表达式提取图片URL
                    image_match = re.search(r'"hiRes":"([^"]+)"', script.text)
                    if image_match:
                        return image_match.group(1)

            # 方法2: 从img标签提取
            main_image = soup.find('img', {'id': 'landingImage'})
            if main_image and main_image.get('src'):
                return main_image.get('src')

            # 方法3: 查找其他可能的主图
            for img in soup.find_all('img'):
                if img.get('data-old-hires'):
                    return img.get('data-old-hires')
        except Exception as e:
            logger.debug(f"提取主图URL时出错: {e}")
        return "未找到图片"

    def process_asins(self, asins, max_workers=10):
        """处理ASIN列表"""
        logger.info(f"🚀 开始处理 {len(asins)} 个ASIN，使用 {max_workers} 个线程")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_asin = {executor.submit(self.get_product_info, asin): asin for asin in asins}

            # 处理结果
            for future in concurrent.futures.as_completed(future_to_asin):
                if self.stop_event.is_set():
                    break

                asin = future_to_asin[future]
                try:
                    result = future.result()
                    with self.lock:
                        self.processed_count += 1
                        if result:
                            self.results.append(result)
                            self.success_count += 1
                            logger.info(f"✅ 成功处理 {asin} ({self.processed_count}/{len(asins)})")
                        else:
                            self.error_count += 1
                            logger.warning(f"❌ 处理失败 {asin} ({self.processed_count}/{len(asins)})")

                        # 定期保存结果
                        if len(self.results) % 10 == 0:
                            self.save_results()

                except Exception as e:
                    logger.error(f"❌ 处理ASIN {asin} 时出现异常: {e}")
                    with self.lock:
                        self.processed_count += 1
                        self.error_count += 1

        # 最终保存
        self.save_results()

        logger.info(f"🎉 处理完成！总计: {self.processed_count}, 成功: {self.success_count}, 失败: {self.error_count}")
        return self.results

    def save_results(self):
        """保存结果到Excel"""
        if not self.results:
            logger.warning("没有结果需要保存")
            return

        try:
            df = pd.DataFrame(self.results)
            filename = 'Brand_品牌产品_纯requests版本.xlsx'
            df.to_excel(filename, index=False)
            logger.info(f"📁 结果已保存到: {filename} ({len(self.results)} 条记录)")
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    print("🎉 纯requests版本Amazon爬虫")
    print("=" * 50)
    print("✅ 基于原始架构的requests实现")
    print("🛡️ 集成增强验证处理能力")
    print("🚀 高效多线程处理")
    print("=" * 50)

    try:
        # 创建爬虫实例
        scraper = PureRequestsAmazonScraper()

        # 加载ASIN数据
        if os.path.exists('asin.xlsx'):
            df = pd.read_excel('asin.xlsx')
            asins = df.iloc[:, 0].astype(str).tolist()
            logger.info(f"成功加载 {len(asins)} 个ASIN")
        else:
            logger.error("❌ 未找到asin.xlsx文件")
            return

        # 开始处理
        results = scraper.process_asins(asins[:100])  # 先处理前100个测试

        print(f"\n🎉 处理完成！")
        print(f"📊 成功处理: {scraper.success_count} 个产品")
        print(f"❌ 处理失败: {scraper.error_count} 个产品")
        print(f"📁 结果已保存到: Brand_品牌产品_纯requests版本.xlsx")

    except KeyboardInterrupt:
        logger.info("🛑 用户中断程序")
        scraper.stop_event.set()
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
