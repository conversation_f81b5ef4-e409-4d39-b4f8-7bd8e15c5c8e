#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行纯requests版本Amazon爬虫
结合原始架构和增强验证处理能力
"""

import sys
import os
import logging
import time
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pure_requests_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_requirements():
    """检查必要的文件和依赖"""
    logger.info("🔍 检查运行环境...")
    
    # 检查必要文件
    required_files = ['proxies.txt', 'asin.xlsx']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            file_size = os.path.getsize(file)
            logger.info(f"   ✅ {file} ({file_size} bytes)")
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 检查依赖库
    try:
        import requests
        import bs4
        import pandas
        logger.info("✅ 所有依赖库已安装")
    except ImportError as e:
        logger.error(f"❌ 缺少依赖库: {e}")
        return False
    
    return True

def load_asins():
    """加载ASIN数据"""
    logger.info("📋 加载ASIN数据...")
    
    try:
        df = pd.read_excel('asin.xlsx')
        asins = df.iloc[:, 0].astype(str).tolist()
        logger.info(f"✅ 成功加载 {len(asins)} 个ASIN")
        return asins
    except Exception as e:
        logger.error(f"❌ 加载ASIN数据失败: {e}")
        return []

def show_configuration():
    """显示配置信息"""
    logger.info("⚙️ 爬虫配置:")
    logger.info("   - 架构: 纯requests (无Selenium依赖)")
    logger.info("   - 验证处理: 5种手机验证破解策略")
    logger.info("   - 代理管理: 自动轮换和冷却")
    logger.info("   - 并发处理: 10个线程")
    logger.info("   - 请求延迟: 1.5-2.5秒")
    logger.info("   - 超时设置: 30秒")
    logger.info("   - 重试次数: 3次")
    logger.info("   - 结果保存: 每10个产品自动保存")

def main():
    """主函数"""
    print("🎉 纯requests版本Amazon爬虫")
    print("=" * 60)
    print("🚀 基于筛品牌最新520250727.py的requests架构")
    print("🛡️ 集成最终运行脚本.py的验证处理能力")
    print("⚡ 高效、稳定、无Selenium依赖")
    print("=" * 60)
    
    try:
        # 1. 检查运行环境
        if not check_requirements():
            logger.error("❌ 运行环境检查失败，程序退出")
            return False
        
        # 2. 加载ASIN数据
        asins = load_asins()
        if not asins:
            logger.error("❌ 无法加载ASIN数据，程序退出")
            return False
        
        # 3. 显示配置信息
        show_configuration()
        
        # 4. 导入并创建爬虫实例
        logger.info("🔨 创建爬虫实例...")
        from 纯requests版本_amazon_scraper import PureRequestsAmazonScraper
        scraper = PureRequestsAmazonScraper()
        
        # 5. 询问处理数量
        print(f"\n📊 数据统计:")
        print(f"   - 总ASIN数量: {len(asins)}")
        print(f"   - 建议测试数量: 50-100")
        print(f"   - 预计处理时间: 约 {len(asins[:100]) * 3 / 60:.1f} 分钟 (100个ASIN)")
        
        # 默认处理前100个进行测试
        test_asins = asins[:100]
        logger.info(f"🎯 将处理前 {len(test_asins)} 个ASIN进行测试")
        
        # 6. 开始处理
        print("\n" + "=" * 60)
        print("🔥 爬虫已启动！")
        print("📊 实时状态将显示在日志中")
        print("📁 结果将保存到 Brand_品牌产品_纯requests版本.xlsx")
        print("🛡️ 验证挑战将自动处理")
        print("🛑 按 Ctrl+C 可以安全停止爬虫")
        print("=" * 60)
        
        start_time = time.time()
        results = scraper.process_asins(test_asins, max_workers=10)
        end_time = time.time()
        
        # 7. 显示结果统计
        print("\n" + "=" * 60)
        print("🎉 处理完成！")
        print("=" * 60)
        print(f"📊 处理统计:")
        print(f"   - 总处理数量: {scraper.processed_count}")
        print(f"   - 成功处理: {scraper.success_count}")
        print(f"   - 处理失败: {scraper.error_count}")
        print(f"   - 成功率: {scraper.success_count/scraper.processed_count*100:.1f}%")
        print(f"   - 总耗时: {(end_time-start_time)/60:.1f} 分钟")
        print(f"   - 平均速度: {scraper.processed_count/(end_time-start_time)*60:.1f} 个/分钟")
        print(f"📁 结果文件: Brand_品牌产品_纯requests版本.xlsx")
        
        # 8. 显示验证处理统计
        if hasattr(scraper.challenge_handler, 'challenge_stats'):
            print(f"\n🛡️ 验证处理统计:")
            stats = scraper.challenge_handler.challenge_stats
            for challenge_type, count in stats.items():
                print(f"   - {challenge_type}: {count} 次")
        
        # 9. 询问是否继续处理剩余ASIN
        if len(asins) > len(test_asins):
            remaining = len(asins) - len(test_asins)
            print(f"\n❓ 还有 {remaining} 个ASIN未处理")
            print("   如需处理全部，请修改代码中的test_asins = asins[:100]为test_asins = asins")
        
        return True
        
    except KeyboardInterrupt:
        logger.info("🛑 用户中断程序")
        if 'scraper' in locals():
            scraper.stop_event.set()
        print("\n✅ 程序已安全停止")
        return False
        
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_asin():
    """测试单个ASIN"""
    print("🧪 单个ASIN测试模式")
    print("=" * 40)
    
    try:
        from 纯requests版本_amazon_scraper import PureRequestsAmazonScraper
        scraper = PureRequestsAmazonScraper()
        
        # 测试多个ASIN确保稳定性
        test_asins = ["B0BSHF7LLL", "B0C1JBQZPX", "B0BSHF7LLL"]  # 测试多个ASIN
        success_count = 0

        for i, test_asin in enumerate(test_asins, 1):
            logger.info(f"🎯 测试ASIN {i}/{len(test_asins)}: {test_asin}")

            result = scraper.get_product_info(test_asin)

            if result:
                success_count += 1
                print(f"✅ 测试 {i} 成功！")
                print("📋 产品信息:")
                for key, value in result.items():
                    print(f"   {key}: {value}")
                print("-" * 50)
            else:
                print(f"❌ 测试 {i} 失败")
                print("-" * 50)

        print(f"\n🎉 测试完成！成功率: {success_count}/{len(test_asins)} ({success_count/len(test_asins)*100:.1f}%)")

        if success_count == len(test_asins):
            print("✅ 所有测试通过，系统运行正常！")
        elif success_count > 0:
            print("⚠️ 部分测试通过，系统基本正常")
        else:
            print("❌ 所有测试失败，需要检查系统")
            
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_asin()
    else:
        main()
