#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极版本 - 集成高级反检测系统的Amazon爬虫
结合所有优化功能和高级反检测技术
"""

import sys
import os
import time
import logging
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultimate_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def apply_all_patches():
    """应用所有补丁和修复"""
    logger.info("🔧 开始应用所有补丁...")

    try:
        # 应用手机验证修复
        if os.path.exists('快速修复_手机验证.py'):
            logger.info("📱 应用手机验证修复...")
            try:
                exec(open('快速修复_手机验证.py', encoding='utf-8').read())
                logger.info("✅ 手机验证修复应用成功")
            except Exception as e:
                logger.warning(f"手机验证修复应用失败: {e}")

        # 尝试导入高级反检测系统（可选）
        if os.path.exists('高级反检测系统.py'):
            logger.info("🛡️ 尝试导入高级反检测系统...")
            try:
                # 检查必要的依赖
                try:
                    import undetected_chromedriver as uc
                    import fake_useragent

                    global AdvancedAntiDetectionSystem
                    exec(open('高级反检测系统.py', encoding='utf-8').read(), globals())
                    logger.info("✅ 高级反检测系统导入成功")
                    return True
                except ImportError as ie:
                    logger.warning(f"高级反检测系统依赖缺失: {ie}")
                    logger.info("💡 将使用基础版本运行")
                    return True
            except Exception as e:
                logger.warning(f"高级反检测系统导入失败: {e}")

        logger.info("✅ 基础补丁应用完成")
        return True

    except Exception as e:
        logger.error(f"应用补丁失败: {e}")
        return False

def create_enhanced_scraper():
    """创建增强版爬虫"""
    logger.info("🚀 创建终极增强版爬虫...")
    
    try:
        # 导入优化版本
        import importlib.util
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)
        
        # 获取必要的类
        OptimizedAmazonScraper = optimized_module.OptimizedAmazonScraper
        ChallengeResult = optimized_module.ChallengeResult
        ChallengeType = optimized_module.ChallengeType
        
        class UltimateAmazonScraper(OptimizedAmazonScraper):
            """终极版Amazon爬虫 - 集成所有反检测技术"""
            
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.anti_detection_system = None
                self.enhanced_features_enabled = 'AdvancedAntiDetectionSystem' in globals()
                logger.info(f"🎯 终极版Amazon爬虫初始化完成，增强功能: {'启用' if self.enhanced_features_enabled else '禁用'}")

            def _init_anti_detection_system(self):
                """延迟初始化反检测系统"""
                if self.anti_detection_system is None and self.enhanced_features_enabled:
                    try:
                        if 'AdvancedAntiDetectionSystem' in globals():
                            self.anti_detection_system = AdvancedAntiDetectionSystem()
                            logger.info("🛡️ 高级反检测系统初始化成功")
                        else:
                            logger.warning("高级反检测系统不可用")
                            self.enhanced_features_enabled = False
                    except Exception as e:
                        logger.error(f"反检测系统初始化失败: {e}")
                        self.enhanced_features_enabled = False
            
            def _handle_phone_verification(self, response, soup, start_time):
                """增强的手机验证处理"""
                logger.info("📱 使用终极版手机验证处理...")
                
                # 首先尝试原有的处理方法
                try:
                    original_result = super()._handle_phone_verification(response, soup, start_time)
                    if original_result.solved:
                        logger.info("✅ 原有方法成功处理手机验证")
                        return original_result
                except Exception as e:
                    logger.warning(f"原有方法失败: {e}")
                
                # 如果原有方法失败且启用了增强功能，使用高级反检测系统
                if self.enhanced_features_enabled:
                    logger.info("🚀 启用高级反检测系统...")
                    
                    self._init_anti_detection_system()
                    
                    if self.anti_detection_system:
                        try:
                            success = self.anti_detection_system.advanced_phone_verification_bypass(response.url)
                            
                            if success:
                                logger.info("🎉 高级反检测系统成功绕过手机验证！")
                                return ChallengeResult(
                                    solved=True,
                                    method="高级反检测系统",
                                    time_taken=time.time() - start_time,
                                    details="使用undetected_chrome和多策略绕过"
                                )
                        except Exception as e:
                            logger.error(f"高级反检测系统失败: {e}")
                
                # 如果所有方法都失败，返回失败结果
                logger.warning("❌ 所有手机验证绕过方法都失败了")
                return ChallengeResult(
                    solved=False,
                    method="所有方法失败",
                    time_taken=time.time() - start_time,
                    details="原有方法和高级反检测系统都无法绕过"
                )
            
            def get_amazon_product_info(self, asin, max_retries=3):
                """增强的产品信息获取"""
                logger.info(f"🎯 使用终极版获取产品信息: {asin}")
                
                # 在获取产品信息前，确保反检测系统已准备就绪
                if self.enhanced_features_enabled:
                    self._init_anti_detection_system()
                
                # 调用父类方法
                return super().get_amazon_product_info(asin, max_retries)
            
            def cleanup(self):
                """清理所有资源"""
                logger.info("🧹 清理终极版爬虫资源...")
                
                # 清理反检测系统
                if self.anti_detection_system:
                    try:
                        self.anti_detection_system.cleanup()
                        logger.info("✅ 反检测系统资源清理完成")
                    except Exception as e:
                        logger.error(f"反检测系统清理失败: {e}")
                
                # 调用父类清理
                super().cleanup()
                logger.info("✅ 终极版爬虫资源清理完成")
            
            def get_status_summary(self):
                """获取状态摘要"""
                summary = super().get_status_summary()
                summary.update({
                    "enhanced_features": self.enhanced_features_enabled,
                    "anti_detection_system": self.anti_detection_system is not None,
                    "version": "Ultimate Enhanced v1.0"
                })
                return summary
        
        logger.info("🎉 终极增强版爬虫创建成功！")
        return UltimateAmazonScraper
        
    except Exception as e:
        logger.error(f"创建增强版爬虫失败: {e}")
        return None

def run_gui_mode():
    """运行GUI模式"""
    logger.info("🖥️ 启动GUI模式...")
    
    try:
        # 导入GUI模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)
        
        ScraperGUI = optimized_module.ScraperGUI
        
        # 创建增强版爬虫
        UltimateAmazonScraper = create_enhanced_scraper()
        if not UltimateAmazonScraper:
            logger.error("无法创建增强版爬虫")
            return
        
        # 启动GUI
        gui = ScraperGUI(UltimateAmazonScraper)
        gui.run()
        
    except Exception as e:
        logger.error(f"GUI模式启动失败: {e}")

def run_cli_mode():
    """运行命令行模式"""
    logger.info("💻 启动命令行模式...")
    
    try:
        # 创建增强版爬虫
        UltimateAmazonScraper = create_enhanced_scraper()
        if not UltimateAmazonScraper:
            logger.error("无法创建增强版爬虫")
            return
        
        # 创建爬虫实例
        scraper = UltimateAmazonScraper()
        
        try:
            # 开始处理
            scraper.start_processing()
            
            # 等待用户中断
            logger.info("🚀 终极版爬虫正在运行...")
            logger.info("按 Ctrl+C 停止运行")
            
            while True:
                time.sleep(10)
                status = scraper.get_status_summary()
                logger.info(f"📊 当前状态: 已处理 {status.get('processed', 0)} 个产品")
                
        except KeyboardInterrupt:
            logger.info("🛑 用户中断，正在停止...")
        finally:
            scraper.cleanup()
            
    except Exception as e:
        logger.error(f"命令行模式运行失败: {e}")

def show_features():
    """显示功能特性"""
    features = """
🎯 终极版Amazon爬虫功能特性

📱 手机验证破解:
   ✅ 5种原有破解策略
   ✅ 高级反检测系统
   ✅ undetected_chrome浏览器
   ✅ 人类行为模拟
   ✅ 浏览器指纹伪造

🛡️ 反检测技术:
   ✅ 动态User-Agent
   ✅ 随机请求头
   ✅ 浏览器指纹生成
   ✅ WebGL/Canvas指纹伪造
   ✅ 音频指纹生成
   ✅ 插件信息模拟

🚀 高级功能:
   ✅ 智能代理管理
   ✅ 自动故障恢复
   ✅ 实时状态监控
   ✅ 详细调试信息
   ✅ 多线程处理
   ✅ GUI和CLI双模式

📊 支持的验证类型:
   ✅ IMAGE_CAPTCHA (图像验证码)
   ✅ CLICK_CONTINUE (点击继续)
   ✅ ROBOT_CHECK (机器人检查)
   ✅ PHONE_VERIFICATION (手机验证) - 增强
   ✅ EMAIL_VERIFICATION (邮箱验证)

🔧 调试和监控:
   ✅ 详细日志记录
   ✅ 页面内容保存
   ✅ 实时状态显示
   ✅ 错误自动恢复
   ✅ 性能统计分析
"""
    print(features)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='终极版Amazon爬虫 - 集成高级反检测系统')
    parser.add_argument('--cli', action='store_true', help='使用命令行模式')
    parser.add_argument('--features', action='store_true', help='显示功能特性')
    
    args = parser.parse_args()
    
    print("🎯 终极版Amazon爬虫 - 集成高级反检测系统")
    print("=" * 60)
    
    if args.features:
        show_features()
        return
    
    # 应用所有补丁
    if not apply_all_patches():
        logger.error("补丁应用失败，退出")
        return
    
    if args.cli:
        run_cli_mode()
    else:
        run_gui_mode()

if __name__ == "__main__":
    main()
