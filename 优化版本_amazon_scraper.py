#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版亚马逊产品爬虫 - 增强反爬虫处理
主要优化：
1. 统一反爬虫检测和处理机制
2. 智能代理管理和健康检查
3. 更真实的浏览器指纹模拟
4. 按需Selenium启动和复用
5. 增强的验证码处理
6. 更细粒度的错误分类和恢复
"""

import requests
import random
import time
import json
import pickle
import os
import re
import logging
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import hashlib
from amazoncaptcha import AmazonCaptcha
import pandas as pd
from tqdm import tqdm
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import queue
import subprocess
import zipfile
import tempfile

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('amazon_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ChallengeType(Enum):
    """验证挑战类型"""
    NONE = "none"
    IMAGE_CAPTCHA = "image_captcha"
    CLICK_CONTINUE = "click_continue"
    ROBOT_CHECK = "robot_check"
    PHONE_VERIFICATION = "phone_verification"
    EMAIL_VERIFICATION = "email_verification"
    BLOCKED = "blocked"

class ProxyStatus(Enum):
    """代理状态"""
    ACTIVE = "active"
    COOLING = "cooling"
    FAILED = "failed"
    BANNED = "banned"

@dataclass
class ProxyInfo:
    """代理信息"""
    protocol: str
    host: str
    port: int
    username: str
    password: str
    status: ProxyStatus = ProxyStatus.ACTIVE
    fail_count: int = 0
    last_used: float = 0
    response_time: float = 0
    success_count: int = 0
    cooling_until: float = 0
    
    @property
    def url(self) -> str:
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def is_available(self) -> bool:
        now = time.time()
        if self.status == ProxyStatus.BANNED:
            return False
        if self.status == ProxyStatus.COOLING and now < self.cooling_until:
            return False
        return self.fail_count < 5

@dataclass
class ChallengeResult:
    """验证挑战结果"""
    challenge_type: ChallengeType
    solved: bool
    method_used: str
    time_taken: float
    error_message: Optional[str] = None

class AntiDetectionManager:
    """反检测管理器"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        self.accept_languages = [
            'en-US,en;q=0.9',
            'en-US,en;q=0.8,zh-CN;q=0.7,zh;q=0.6',
            'en-GB,en;q=0.9,en-US;q=0.8',
            'en-US,en;q=0.9,es;q=0.8'
        ]
        
        self.accept_encodings = [
            'gzip, deflate, br',
            'gzip, deflate',
            'gzip, deflate, br, zstd'
        ]
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': random.choice(self.accept_languages),
            'Accept-Encoding': random.choice(self.accept_encodings),
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
    
    def get_random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0) -> float:
        """获取随机延迟时间"""
        # 使用正态分布使延迟更自然
        mean = (min_delay + max_delay) / 2
        std = (max_delay - min_delay) / 6
        delay = random.normalvariate(mean, std)
        return max(min_delay, min(max_delay, delay))

class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxy_file: str = 'proxies.txt'):
        self.proxies: List[ProxyInfo] = []
        self.proxy_lock = threading.Lock()
        self.load_proxies(proxy_file)
    
    def load_proxies(self, proxy_file: str):
        """加载代理列表"""
        if not os.path.exists(proxy_file):
            logger.warning(f"代理文件不存在: {proxy_file}")
            return
        
        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析代理块
            proxy_blocks = re.split(r'\n\s*\n', content.strip())
            
            for block in proxy_blocks:
                lines = [line.strip() for line in block.split('\n') if line.strip()]
                if len(lines) >= 5:
                    try:
                        protocol = lines[0].split(': ')[1].strip()
                        host = lines[1].split(': ')[1].strip()
                        port = int(lines[2].split(': ')[1].strip())
                        username = lines[3].split(': ')[1].strip()
                        password = lines[4].split(': ')[1].strip()
                        
                        proxy = ProxyInfo(protocol, host, port, username, password)
                        self.proxies.append(proxy)
                    except (IndexError, ValueError) as e:
                        logger.warning(f"解析代理块失败: {e}")
            
            logger.info(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            logger.error(f"加载代理文件失败: {e}")
    
    def get_best_proxy(self) -> Optional[ProxyInfo]:
        """获取最佳可用代理"""
        with self.proxy_lock:
            available_proxies = [p for p in self.proxies if p.is_available]
            
            if not available_proxies:
                logger.warning("没有可用的代理")
                return None
            
            # 按成功率和响应时间排序
            available_proxies.sort(key=lambda p: (
                -p.success_count / max(p.success_count + p.fail_count, 1),
                p.response_time
            ))
            
            return available_proxies[0]
    
    def mark_proxy_success(self, proxy: ProxyInfo, response_time: float):
        """标记代理成功"""
        with self.proxy_lock:
            proxy.success_count += 1
            proxy.response_time = response_time
            proxy.last_used = time.time()
            if proxy.status == ProxyStatus.FAILED:
                proxy.status = ProxyStatus.ACTIVE
                proxy.fail_count = 0
    
    def mark_proxy_failed(self, proxy: ProxyInfo, ban_duration: float = 300):
        """标记代理失败"""
        with self.proxy_lock:
            proxy.fail_count += 1
            proxy.last_used = time.time()
            
            if proxy.fail_count >= 5:
                proxy.status = ProxyStatus.BANNED
                logger.warning(f"代理被永久禁用: {proxy.url}")
            elif proxy.fail_count >= 3:
                proxy.status = ProxyStatus.COOLING
                proxy.cooling_until = time.time() + ban_duration
                logger.info(f"代理进入冷却期: {proxy.url}, 冷却时间: {ban_duration}秒")
            else:
                proxy.status = ProxyStatus.FAILED

    def mark_proxy_cooling(self, proxy: ProxyInfo, cooling_time: float = 900):
        """标记代理需要冷却"""
        with self.proxy_lock:
            proxy.status = ProxyStatus.COOLING
            proxy.cooling_until = time.time() + cooling_time
            logger.info(f"代理设置冷却: {proxy.url}, 冷却时间: {cooling_time}秒")

class ChallengeDetector:
    """验证挑战检测器"""
    
    @staticmethod
    def detect_challenge(response: requests.Response, soup: BeautifulSoup) -> ChallengeType:
        """检测验证挑战类型"""
        url = response.url.lower()
        content = soup.get_text().lower()
        
        # 检测图片验证码
        if ('captcha' in url or 'captcha' in content or 
            soup.find('img', {'alt': re.compile(r'captcha', re.I)}) or
            soup.find('form', {'action': re.compile(r'captcha', re.I)})):
            return ChallengeType.IMAGE_CAPTCHA
        
        # 检测点击继续验证
        if ('continue shopping' in content or 
            'click here to continue' in content or
            soup.find('a', string=re.compile(r'continue', re.I))):
            return ChallengeType.CLICK_CONTINUE
        
        # 检测机器人检查
        if ('robot' in content or 'automation' in content or
            'unusual traffic' in content):
            return ChallengeType.ROBOT_CHECK
        
        # 检测手机验证
        if 'phone' in content and 'verify' in content:
            return ChallengeType.PHONE_VERIFICATION
        
        # 检测邮箱验证
        if 'email' in content and 'verify' in content:
            return ChallengeType.EMAIL_VERIFICATION
        
        # 检测IP被封
        if ('blocked' in content or 'access denied' in content or
            response.status_code == 403):
            return ChallengeType.BLOCKED
        
        return ChallengeType.NONE

class ChallengeHandler:
    """验证挑战处理器"""
    
    def __init__(self, session: requests.Session, proxy_manager: ProxyManager):
        self.session = session
        self.proxy_manager = proxy_manager
        self.selenium_driver = None
    
    def handle_challenge(self, challenge_type: ChallengeType, response: requests.Response, 
                        soup: BeautifulSoup) -> ChallengeResult:
        """处理验证挑战"""
        start_time = time.time()
        
        try:
            if challenge_type == ChallengeType.IMAGE_CAPTCHA:
                return self._handle_image_captcha(response, soup, start_time)
            elif challenge_type == ChallengeType.CLICK_CONTINUE:
                return self._handle_click_continue(response, soup, start_time)
            elif challenge_type == ChallengeType.ROBOT_CHECK:
                return self._handle_robot_check(response, soup, start_time)
            elif challenge_type == ChallengeType.PHONE_VERIFICATION:
                return self._handle_phone_verification(response, soup, start_time)
            elif challenge_type == ChallengeType.EMAIL_VERIFICATION:
                return self._handle_email_verification(response, soup, start_time)
            else:
                return ChallengeResult(
                    challenge_type=challenge_type,
                    solved=False,
                    method_used="unsupported",
                    time_taken=time.time() - start_time,
                    error_message=f"不支持的验证类型: {challenge_type}"
                )
        except Exception as e:
            return ChallengeResult(
                challenge_type=challenge_type,
                solved=False,
                method_used="error",
                time_taken=time.time() - start_time,
                error_message=str(e)
            )
    
    def _handle_image_captcha(self, response: requests.Response, soup: BeautifulSoup, 
                             start_time: float) -> ChallengeResult:
        """处理图片验证码"""
        try:
            # 首先尝试使用requests方式
            captcha_img = soup.find('img', {'alt': re.compile(r'captcha', re.I)})
            if captcha_img and captcha_img.get('src'):
                img_url = urljoin(response.url, captcha_img['src'])
                
                # 下载验证码图片
                img_response = self.session.get(img_url, timeout=10)
                if img_response.status_code == 200:
                    # 使用amazoncaptcha库解析
                    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                        tmp_file.write(img_response.content)
                        tmp_file.flush()
                        
                        captcha = AmazonCaptcha(tmp_file.name)
                        solution = captcha.solve()
                        
                        os.unlink(tmp_file.name)
                        
                        if solution and solution != 'Not solved':
                            # 提交验证码
                            form = soup.find('form')
                            if form:
                                action = form.get('action', '')
                                if not action.startswith('http'):
                                    action = urljoin(response.url, action)
                                
                                form_data = {}
                                for input_tag in form.find_all('input'):
                                    name = input_tag.get('name')
                                    value = input_tag.get('value', '')
                                    if name:
                                        if input_tag.get('type') == 'text':
                                            form_data[name] = solution
                                        else:
                                            form_data[name] = value
                                
                                submit_response = self.session.post(action, data=form_data, timeout=15)
                                
                                if submit_response.status_code == 200:
                                    return ChallengeResult(
                                        challenge_type=ChallengeType.IMAGE_CAPTCHA,
                                        solved=True,
                                        method_used="requests_amazoncaptcha",
                                        time_taken=time.time() - start_time
                                    )
            
            # 如果requests方式失败，尝试Selenium
            return self._handle_captcha_with_selenium(response.url, start_time)
            
        except Exception as e:
            logger.error(f"处理图片验证码失败: {e}")
            return ChallengeResult(
                challenge_type=ChallengeType.IMAGE_CAPTCHA,
                solved=False,
                method_used="requests_amazoncaptcha",
                time_taken=time.time() - start_time,
                error_message=str(e)
            )
    
    def _handle_click_continue(self, response: requests.Response, soup: BeautifulSoup, 
                              start_time: float) -> ChallengeResult:
        """处理点击继续验证"""
        try:
            # 查找继续链接
            continue_link = soup.find('a', string=re.compile(r'continue', re.I))
            if not continue_link:
                continue_link = soup.find('a', href=re.compile(r'continue', re.I))
            
            if continue_link and continue_link.get('href'):
                continue_url = urljoin(response.url, continue_link['href'])
                
                # 添加随机延迟模拟人类行为
                time.sleep(random.uniform(2, 5))
                
                continue_response = self.session.get(continue_url, timeout=15)
                
                if continue_response.status_code == 200:
                    return ChallengeResult(
                        challenge_type=ChallengeType.CLICK_CONTINUE,
                        solved=True,
                        method_used="requests_click",
                        time_taken=time.time() - start_time
                    )
            
            return ChallengeResult(
                challenge_type=ChallengeType.CLICK_CONTINUE,
                solved=False,
                method_used="requests_click",
                time_taken=time.time() - start_time,
                error_message="未找到继续链接"
            )
            
        except Exception as e:
            logger.error(f"处理点击继续验证失败: {e}")
            return ChallengeResult(
                challenge_type=ChallengeType.CLICK_CONTINUE,
                solved=False,
                method_used="requests_click",
                time_taken=time.time() - start_time,
                error_message=str(e)
            )
    
    def _handle_robot_check(self, response: requests.Response, soup: BeautifulSoup, 
                           start_time: float) -> ChallengeResult:
        """处理机器人检查"""
        # 对于机器人检查，通常需要更换代理和等待
        logger.warning("检测到机器人检查，建议更换代理")
        
        # 标记当前代理需要冷却
        current_proxy = getattr(self.session, '_current_proxy', None)
        if current_proxy:
            self.proxy_manager.mark_proxy_failed(current_proxy, ban_duration=600)  # 10分钟冷却
        
        return ChallengeResult(
            challenge_type=ChallengeType.ROBOT_CHECK,
            solved=False,
            method_used="proxy_rotation",
            time_taken=time.time() - start_time,
            error_message="需要更换代理"
        )

    def _handle_phone_verification(self, response: requests.Response, soup: BeautifulSoup,
                                  start_time: float) -> ChallengeResult:
        """处理手机验证"""
        logger.warning("检测到手机验证，开始分析页面内容...")

        # 打印响应内容用于分析
        self._debug_phone_verification_page(response, soup)

        # 尝试破解手机验证
        if self._attempt_phone_verification_bypass(response, soup):
            logger.info("手机验证破解成功！")
            return ChallengeResult(
                challenge_type=ChallengeType.PHONE_VERIFICATION,
                solved=True,
                method_used="phone_verification_bypass",
                time_taken=time.time() - start_time
            )

        # 如果破解失败，标记代理需要冷却
        logger.warning("手机验证破解失败，将当前代理标记为需要长时间冷却")
        current_proxy = getattr(self.session, '_current_proxy', None)
        if current_proxy:
            # 设置长时间冷却（30分钟）
            self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=1800)
            logger.info(f"代理 {current_proxy.host}:{current_proxy.port} 已设置30分钟冷却")

        return ChallengeResult(
            challenge_type=ChallengeType.PHONE_VERIFICATION,
            solved=False,
            method_used="proxy_cooling",
            time_taken=time.time() - start_time,
            error_message="手机验证需要更换代理"
        )

    def _handle_email_verification(self, response: requests.Response, soup: BeautifulSoup,
                                  start_time: float) -> ChallengeResult:
        """处理邮箱验证"""
        logger.warning("检测到邮箱验证，将当前代理标记为需要冷却")

        # 获取当前代理并标记为需要冷却
        current_proxy = getattr(self.session, '_current_proxy', None)
        if current_proxy:
            # 设置中等时间冷却（15分钟）
            self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=900)
            logger.info(f"代理 {current_proxy.host}:{current_proxy.port} 已设置15分钟冷却")

        return ChallengeResult(
            challenge_type=ChallengeType.EMAIL_VERIFICATION,
            solved=False,
            method_used="proxy_cooling",
            time_taken=time.time() - start_time,
            error_message="邮箱验证需要更换代理"
        )

    def _debug_phone_verification_page(self, response: requests.Response, soup: BeautifulSoup):
        """调试手机验证页面内容"""
        logger.info("=" * 80)
        logger.info("🔍 手机验证页面分析开始")
        logger.info("=" * 80)

        # 打印基本信息
        logger.info(f"📍 URL: {response.url}")
        logger.info(f"📊 状态码: {response.status_code}")
        logger.info(f"📏 内容长度: {len(response.content)} bytes")

        # 打印响应头
        logger.info("📋 响应头:")
        for key, value in response.headers.items():
            if key.lower() in ['content-type', 'set-cookie', 'location', 'server']:
                logger.info(f"  {key}: {value}")

        # 打印页面标题
        title = soup.find('title')
        if title:
            logger.info(f"📄 页面标题: {title.get_text().strip()}")

        # 查找关键元素
        logger.info("🔍 关键元素分析:")

        # 查找表单
        forms = soup.find_all('form')
        logger.info(f"📝 找到 {len(forms)} 个表单")
        for i, form in enumerate(forms):
            action = form.get('action', '')
            method = form.get('method', 'GET')
            logger.info(f"  表单 {i+1}: {method} -> {action}")

            # 查找输入字段
            inputs = form.find_all('input')
            for inp in inputs:
                name = inp.get('name', '')
                input_type = inp.get('type', '')
                value = inp.get('value', '')
                if name:
                    logger.info(f"    输入字段: {name} ({input_type}) = {value}")

        # 查找手机号输入框
        phone_inputs = soup.find_all('input', {'type': ['tel', 'text']})
        phone_inputs.extend(soup.find_all('input', {'name': re.compile(r'phone|mobile', re.I)}))
        logger.info(f"📱 找到 {len(phone_inputs)} 个可能的手机号输入框")

        # 查找按钮
        buttons = soup.find_all(['button', 'input'], {'type': ['submit', 'button']})
        logger.info(f"🔘 找到 {len(buttons)} 个按钮")
        for button in buttons:
            text = button.get_text().strip() or button.get('value', '')
            logger.info(f"  按钮: {text}")

        # 查找错误信息
        error_elements = soup.find_all(['div', 'span'], class_=re.compile(r'error|alert|warning', re.I))
        if error_elements:
            logger.info("⚠️ 错误信息:")
            for error in error_elements:
                error_text = error.get_text().strip()
                if error_text:
                    logger.info(f"  {error_text}")

        # 查找跳过链接
        skip_links = soup.find_all('a', string=re.compile(r'skip|later|not now', re.I))
        if skip_links:
            logger.info("⏭️ 找到跳过链接:")
            for link in skip_links:
                href = link.get('href', '')
                text = link.get_text().strip()
                logger.info(f"  {text}: {href}")

        # 保存页面内容到文件用于进一步分析
        try:
            with open('phone_verification_debug.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            logger.info("💾 页面内容已保存到 phone_verification_debug.html")
        except Exception as e:
            logger.error(f"保存页面内容失败: {e}")

        # 打印页面文本内容（前1000字符）
        page_text = soup.get_text().strip()
        logger.info("📄 页面文本内容（前1000字符）:")
        logger.info("-" * 40)
        logger.info(page_text[:1000])
        if len(page_text) > 1000:
            logger.info("... (内容被截断)")
        logger.info("-" * 40)

        logger.info("=" * 80)
        logger.info("🔍 手机验证页面分析结束")
        logger.info("=" * 80)

    def _attempt_phone_verification_bypass(self, response: requests.Response, soup: BeautifulSoup) -> bool:
        """尝试破解手机验证"""
        logger.info("🚀 开始尝试破解手机验证...")

        try:
            # 策略1: 查找跳过链接
            if self._try_skip_phone_verification(response, soup):
                return True

            # 策略2: 尝试使用虚假手机号
            if self._try_fake_phone_number(response, soup):
                return True

            # 策略3: 尝试返回上一页
            if self._try_go_back(response, soup):
                return True

            # 策略4: 尝试直接访问产品页面
            if self._try_direct_product_access(response):
                return True

            # 策略5: 使用Selenium进行高级绕过
            if self._use_selenium_bypass(response):
                return True

            logger.warning("所有破解策略都失败了")
            return False

        except Exception as e:
            logger.error(f"破解手机验证时出错: {e}")
            return False

    def _try_skip_phone_verification(self, response: requests.Response, soup: BeautifulSoup) -> bool:
        """尝试跳过手机验证"""
        logger.info("🔄 策略1: 查找跳过链接...")

        # 查找各种可能的跳过链接
        skip_patterns = [
            r'skip|later|not now|maybe later|remind me later',
            r'continue without|without verification',
            r'i don\'t have|no phone|no mobile'
        ]

        for pattern in skip_patterns:
            skip_links = soup.find_all('a', string=re.compile(pattern, re.I))
            skip_links.extend(soup.find_all('a', {'title': re.compile(pattern, re.I)}))

            for link in skip_links:
                href = link.get('href')
                if href:
                    try:
                        if not href.startswith('http'):
                            href = urljoin(response.url, href)

                        logger.info(f"找到跳过链接: {href}")

                        # 点击跳过链接
                        skip_response = self.session.get(href, timeout=15)
                        if skip_response.status_code == 200:
                            # 检查是否成功跳过
                            if 'phone' not in skip_response.url.lower() and 'verification' not in skip_response.url.lower():
                                logger.info("✅ 成功通过跳过链接绕过手机验证")
                                return True
                    except Exception as e:
                        logger.error(f"点击跳过链接失败: {e}")

        return False

    def _try_fake_phone_number(self, response: requests.Response, soup: BeautifulSoup) -> bool:
        """尝试使用虚假手机号"""
        logger.info("🔄 策略2: 尝试使用虚假手机号...")

        # 查找表单
        form = soup.find('form')
        if not form:
            logger.warning("未找到表单")
            return False

        action = form.get('action', '')
        if not action.startswith('http'):
            action = urljoin(response.url, action)

        # 构建表单数据
        form_data = {}

        # 获取所有隐藏字段
        for hidden_input in form.find_all('input', {'type': 'hidden'}):
            name = hidden_input.get('name')
            value = hidden_input.get('value', '')
            if name:
                form_data[name] = value

        # 查找手机号输入框
        phone_input = form.find('input', {'type': ['tel', 'text']})
        if not phone_input:
            phone_input = form.find('input', {'name': re.compile(r'phone|mobile', re.I)})

        if phone_input:
            phone_name = phone_input.get('name')
            if phone_name:
                # 使用虚假手机号
                fake_phones = [
                    '******-0123',
                    '555-0123',
                    '1234567890',
                    '******-555-0199'
                ]

                for fake_phone in fake_phones:
                    form_data[phone_name] = fake_phone

                    try:
                        logger.info(f"尝试提交虚假手机号: {fake_phone}")
                        submit_response = self.session.post(action, data=form_data, timeout=15)

                        if submit_response.status_code == 200:
                            # 检查是否成功
                            if 'phone' not in submit_response.url.lower() and 'verification' not in submit_response.url.lower():
                                logger.info("✅ 成功使用虚假手机号绕过验证")
                                return True
                    except Exception as e:
                        logger.error(f"提交虚假手机号失败: {e}")

        return False

    def _try_go_back(self, response: requests.Response, soup: BeautifulSoup) -> bool:
        """尝试返回上一页"""
        logger.info("🔄 策略3: 尝试返回上一页...")

        # 查找返回按钮或链接
        back_elements = soup.find_all(['a', 'button'], string=re.compile(r'back|return|go back', re.I))
        back_elements.extend(soup.find_all(['a', 'button'], {'title': re.compile(r'back|return', re.I)}))

        for element in back_elements:
            if element.name == 'a':
                href = element.get('href')
                if href:
                    try:
                        if not href.startswith('http'):
                            href = urljoin(response.url, href)

                        logger.info(f"尝试返回链接: {href}")
                        back_response = self.session.get(href, timeout=15)

                        if back_response.status_code == 200:
                            if 'phone' not in back_response.url.lower():
                                logger.info("✅ 成功通过返回链接绕过验证")
                                return True
                    except Exception as e:
                        logger.error(f"返回链接失败: {e}")

        return False

    def _try_direct_product_access(self, response: requests.Response) -> bool:
        """尝试直接访问产品页面"""
        logger.info("🔄 策略4: 尝试直接访问产品页面...")

        # 从当前URL中提取ASIN
        current_url = response.url
        asin_match = re.search(r'/dp/([A-Z0-9]{10})', current_url)

        if asin_match:
            asin = asin_match.group(1)
            direct_url = f"https://www.amazon.com/dp/{asin}"

            try:
                logger.info(f"尝试直接访问: {direct_url}")

                # 使用新的会话或清除cookies
                direct_response = self.session.get(direct_url, timeout=15)

                if direct_response.status_code == 200:
                    # 检查是否成功访问产品页面
                    if 'phone' not in direct_response.url.lower() and 'verification' not in direct_response.url.lower():
                        # 进一步检查是否是真正的产品页面
                        direct_soup = BeautifulSoup(direct_response.content, 'html.parser')
                        if direct_soup.find('div', {'id': 'dp'}) or direct_soup.find('div', {'data-asin': asin}):
                            logger.info("✅ 成功直接访问产品页面")
                            return True
            except Exception as e:
                logger.error(f"直接访问产品页面失败: {e}")

        return False

    def _use_selenium_bypass(self, response: requests.Response) -> bool:
        """使用Selenium进行高级绕过"""
        logger.info("🔄 策略5: 使用Selenium进行高级绕过...")

        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options

            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置代理
            current_proxy = getattr(self.session, '_current_proxy', None)
            if current_proxy:
                proxy_url = f"{current_proxy.host}:{current_proxy.port}"
                chrome_options.add_argument(f'--proxy-server=socks5://{proxy_url}')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                # 设置反检测脚本
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                # 访问页面
                driver.get(response.url)

                # 等待页面加载
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                # 尝试各种绕过策略

                # 1. 查找并点击跳过按钮
                skip_selectors = [
                    "a[href*='skip']",
                    "button[data-action*='skip']",
                    "a:contains('Skip')",
                    "a:contains('Later')",
                    "a:contains('Not now')"
                ]

                for selector in skip_selectors:
                    try:
                        skip_element = driver.find_element(By.CSS_SELECTOR, selector)
                        if skip_element.is_displayed():
                            logger.info(f"找到跳过元素: {selector}")
                            skip_element.click()
                            time.sleep(2)

                            # 检查是否成功跳过
                            if 'phone' not in driver.current_url.lower():
                                logger.info("✅ Selenium成功绕过手机验证")
                                return True
                    except:
                        continue

                # 2. 尝试JavaScript绕过
                bypass_scripts = [
                    "window.history.back();",
                    "window.location.href = window.location.href.replace(/phone|verification/gi, '');",
                    "document.querySelector('form').style.display = 'none';"
                ]

                for script in bypass_scripts:
                    try:
                        driver.execute_script(script)
                        time.sleep(2)

                        if 'phone' not in driver.current_url.lower():
                            logger.info("✅ JavaScript绕过成功")
                            return True
                    except:
                        continue

            finally:
                driver.quit()

        except ImportError:
            logger.warning("Selenium未安装，跳过Selenium绕过策略")
        except Exception as e:
            logger.error(f"Selenium绕过失败: {e}")

        return False

    def _handle_captcha_with_selenium(self, url: str, start_time: float) -> ChallengeResult:
        """使用Selenium处理验证码"""
        try:
            if not self.selenium_driver:
                self._init_selenium_driver()

            self.selenium_driver.get(url)
            time.sleep(3)

            # 查找验证码图片
            captcha_img = self.selenium_driver.find_element("css selector", "img[alt*='captcha' i]")
            if captcha_img:
                # 截图并解析验证码
                screenshot = captcha_img.screenshot_as_png

                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                    tmp_file.write(screenshot)
                    tmp_file.flush()

                    captcha = AmazonCaptcha(tmp_file.name)
                    solution = captcha.solve()

                    os.unlink(tmp_file.name)

                    if solution and solution != 'Not solved':
                        # 输入验证码
                        input_field = self.selenium_driver.find_element("css selector", "input[type='text']")
                        input_field.clear()
                        input_field.send_keys(solution)

                        # 提交
                        submit_btn = self.selenium_driver.find_element("css selector", "button[type='submit'], input[type='submit']")
                        submit_btn.click()

                        time.sleep(3)

                        # 检查是否成功
                        if 'captcha' not in self.selenium_driver.current_url.lower():
                            # 更新session的cookies
                            selenium_cookies = self.selenium_driver.get_cookies()
                            for cookie in selenium_cookies:
                                self.session.cookies.set(cookie['name'], cookie['value'])

                            return ChallengeResult(
                                challenge_type=ChallengeType.IMAGE_CAPTCHA,
                                solved=True,
                                method_used="selenium_amazoncaptcha",
                                time_taken=time.time() - start_time
                            )

            return ChallengeResult(
                challenge_type=ChallengeType.IMAGE_CAPTCHA,
                solved=False,
                method_used="selenium_amazoncaptcha",
                time_taken=time.time() - start_time,
                error_message="Selenium验证码处理失败"
            )

        except Exception as e:
            logger.error(f"Selenium处理验证码失败: {e}")
            return ChallengeResult(
                challenge_type=ChallengeType.IMAGE_CAPTCHA,
                solved=False,
                method_used="selenium_amazoncaptcha",
                time_taken=time.time() - start_time,
                error_message=str(e)
            )

    def _init_selenium_driver(self):
        """初始化Selenium驱动"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-javascript')

            # 设置用户代理
            anti_detection = AntiDetectionManager()
            user_agent = random.choice(anti_detection.user_agents)
            chrome_options.add_argument(f'--user-agent={user_agent}')

            # 如果有代理，设置代理
            current_proxy = getattr(self.session, '_current_proxy', None)
            if current_proxy:
                if current_proxy.username and current_proxy.password:
                    # 创建代理认证插件
                    self._create_proxy_auth_extension(current_proxy)
                    chrome_options.add_extension('proxy_auth_plugin.zip')
                else:
                    chrome_options.add_argument(f'--proxy-server={current_proxy.protocol}://{current_proxy.host}:{current_proxy.port}')

            self.selenium_driver = webdriver.Chrome(options=chrome_options)
            self.selenium_driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        except Exception as e:
            logger.error(f"初始化Selenium驱动失败: {e}")
            raise

    def _create_proxy_auth_extension(self, proxy: ProxyInfo):
        """创建代理认证扩展"""
        manifest_json = """
        {
            "version": "1.0.0",
            "manifest_version": 2,
            "name": "Chrome Proxy",
            "permissions": [
                "proxy",
                "tabs",
                "unlimitedStorage",
                "storage",
                "<all_urls>",
                "webRequest",
                "webRequestBlocking"
            ],
            "background": {
                "scripts": ["background.js"]
            },
            "minimum_chrome_version":"22.0.0"
        }
        """

        background_js = f"""
        var config = {{
            mode: "fixed_servers",
            rules: {{
                singleProxy: {{
                    scheme: "{proxy.protocol}",
                    host: "{proxy.host}",
                    port: parseInt({proxy.port})
                }},
                bypassList: ["localhost"]
            }}
        }};

        chrome.proxy.settings.set({{value: config, scope: "regular"}}, function() {{}});

        function callbackFn(details) {{
            return {{
                authCredentials: {{
                    username: "{proxy.username}",
                    password: "{proxy.password}"
                }}
            }};
        }}

        chrome.webRequest.onAuthRequired.addListener(
            callbackFn,
            {{urls: ["<all_urls>"]}},
            ['blocking']
        );
        """

        with zipfile.ZipFile('proxy_auth_plugin.zip', 'w') as zp:
            zp.writestr("manifest.json", manifest_json)
            zp.writestr("background.js", background_js)

class OptimizedAmazonScraper:
    """优化版亚马逊爬虫"""

    def __init__(self, max_workers: int = 20):
        self.max_workers = max_workers
        self.session = requests.Session()
        self.anti_detection = AntiDetectionManager()
        self.proxy_manager = ProxyManager()
        self.challenge_handler = ChallengeHandler(self.session, self.proxy_manager)
        self.cookies_file = 'amazon_cookies_optimized.pkl'
        self.results = []
        self.processed_count = 0
        self.total_count = 0

        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'challenges_detected': 0,
            'challenges_solved': 0,
            'proxy_switches': 0
        }

        self._setup_session()

    def _setup_session(self):
        """设置会话"""
        self.session.headers.update(self.anti_detection.get_random_headers())

        # 设置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def _get_with_proxy_rotation(self, url: str, **kwargs) -> Tuple[Optional[requests.Response], Optional[ProxyInfo]]:
        """使用代理轮换发送请求"""
        max_attempts = 3

        for attempt in range(max_attempts):
            proxy = self.proxy_manager.get_best_proxy()
            if not proxy:
                logger.error("没有可用的代理")
                return None, None

            try:
                # 设置代理
                proxies = {'http': proxy.url, 'https': proxy.url}
                self.session.proxies.update(proxies)
                self.session._current_proxy = proxy

                # 添加随机延迟
                delay = self.anti_detection.get_random_delay()
                logger.info(f"请求 {url} 前等待 {delay:.2f} 秒...")
                time.sleep(delay)

                # 发送请求
                start_time = time.time()
                response = self.session.get(url, timeout=15, **kwargs)
                response_time = time.time() - start_time

                self.stats['total_requests'] += 1

                if response.status_code == 200:
                    self.proxy_manager.mark_proxy_success(proxy, response_time)
                    self.stats['successful_requests'] += 1
                    logger.info(f"请求成功, 状态码: {response.status_code}, 用时: {response_time:.2f}秒")
                    return response, proxy
                else:
                    logger.warning(f"请求返回状态码: {response.status_code}")
                    self.proxy_manager.mark_proxy_failed(proxy)

            except Exception as e:
                logger.error(f"请求失败: {e}")
                self.proxy_manager.mark_proxy_failed(proxy)
                self.stats['failed_requests'] += 1

        return None, None

    def _handle_response_challenges(self, response: requests.Response) -> bool:
        """处理响应中的验证挑战"""
        soup = BeautifulSoup(response.content, 'html.parser')
        challenge_type = ChallengeDetector.detect_challenge(response, soup)

        if challenge_type != ChallengeType.NONE:
            self.stats['challenges_detected'] += 1
            logger.warning(f"检测到验证挑战: {challenge_type}")

            result = self.challenge_handler.handle_challenge(challenge_type, response, soup)

            if result.solved:
                self.stats['challenges_solved'] += 1
                logger.info(f"验证挑战已解决: {result.method_used}, 耗时: {result.time_taken:.2f}秒")
                return True
            else:
                logger.error(f"验证挑战解决失败: {result.error_message}")
                return False

        return True

    def get_product_info(self, asin: str) -> Optional[Dict[str, Any]]:
        """获取产品信息"""
        url = f"https://www.amazon.com/dp/{asin}"

        response, proxy = self._get_with_proxy_rotation(url)
        if not response:
            return None

        # 处理验证挑战
        if not self._handle_response_challenges(response):
            return None

        try:
            soup = BeautifulSoup(response.content, 'html.parser')

            # 检查页面语言
            if not self._is_english_page(soup):
                logger.warning(f"ASIN {asin}: 页面不是英文")
                return None

            # 检查产品是否断货
            if not self._is_product_out_of_stock(soup):
                logger.info(f"ASIN {asin}: 产品有货，不符合条件，跳过")
                return None

            # 提取产品信息
            product_info = self._extract_product_info(soup, asin)

            if product_info:
                logger.info(f"ASIN {asin}: 产品信息提取成功")
                return product_info
            else:
                logger.warning(f"ASIN {asin}: 产品信息提取失败")
                return None

        except Exception as e:
            logger.error(f"处理产品 {asin} 时出错: {e}")
            return None

    def _is_english_page(self, soup: BeautifulSoup) -> bool:
        """检查页面是否为英文"""
        # 检查HTML lang属性
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            lang = html_tag['lang'].lower()
            if lang.startswith('en'):
                return True

        # 检查页面内容中的英文关键词
        text = soup.get_text().lower()
        english_keywords = ['add to cart', 'buy now', 'price', 'shipping', 'reviews']
        english_count = sum(1 for keyword in english_keywords if keyword in text)

        return english_count >= 3

    def _is_product_out_of_stock(self, soup: BeautifulSoup) -> bool:
        """检查产品是否断货"""
        # 检查断货关键词
        text = soup.get_text().lower()
        out_of_stock_keywords = [
            'currently unavailable',
            'out of stock',
            'temporarily out of stock',
            'item is not available',
            'no longer available'
        ]

        for keyword in out_of_stock_keywords:
            if keyword in text:
                return True

        # 检查购买按钮是否存在
        buy_buttons = soup.find_all(['input', 'button'], {'id': re.compile(r'add.*cart', re.I)})
        if not buy_buttons:
            # 如果没有购买按钮，可能是断货
            return True

        return False

    def _extract_product_info(self, soup: BeautifulSoup, asin: str) -> Optional[Dict[str, Any]]:
        """提取产品信息"""
        try:
            product_info = {'asin': asin}

            # 提取标题
            title_selectors = [
                '#productTitle',
                '.product-title',
                'h1.a-size-large'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    product_info['title'] = title_elem.get_text().strip()
                    break

            # 提取品牌
            brand = self._extract_brand(soup)
            if not brand:
                logger.warning(f"ASIN {asin}: 未找到品牌信息")
                return None

            product_info['brand'] = brand

            # 检查品牌唯一性
            if not self._is_brand_unique(brand):
                logger.info(f"ASIN {asin}: 品牌 {brand} 不是唯一的，跳过")
                return None

            # 提取评论数
            reviews_count = self._extract_reviews_count(soup)
            if reviews_count is None or not (20 <= reviews_count <= 2000):
                logger.info(f"ASIN {asin}: 评论数 {reviews_count} 不在要求范围内(20-2000)，跳过")
                return None

            product_info['reviews_count'] = reviews_count

            # 提取销售排名
            sales_rank = self._extract_sales_rank(soup)
            product_info['sales_rank'] = sales_rank

            # 提取价格信息
            price_info = self._extract_price_info(soup)
            product_info.update(price_info)

            return product_info

        except Exception as e:
            logger.error(f"提取产品信息失败: {e}")
            return None

    def _extract_brand(self, soup: BeautifulSoup) -> Optional[str]:
        """提取品牌信息"""
        # 尝试多种品牌提取方式
        brand_selectors = [
            'a[data-attribute="brand"]',
            '.po-brand .po-break-word',
            '#bylineInfo',
            '.author .contributorNameID',
            'span.a-size-base.po-break-word'
        ]

        for selector in brand_selectors:
            brand_elem = soup.select_one(selector)
            if brand_elem:
                brand_text = brand_elem.get_text().strip()
                # 清理品牌文本
                brand = re.sub(r'^(Brand:|Visit the|by)\s*', '', brand_text, flags=re.I).strip()
                if brand and len(brand) > 1:
                    return brand

        # 从页面文本中查找品牌
        text = soup.get_text()
        brand_match = re.search(r'Brand:\s*([^\n]+)', text)
        if brand_match:
            return brand_match.group(1).strip()

        return None

    def _is_brand_unique(self, brand: str) -> bool:
        """检查品牌是否唯一"""
        # 这里应该实现品牌搜索逻辑
        # 为了简化，这里返回True，实际应用中需要搜索Amazon
        search_url = f"https://www.amazon.com/s?k={brand}&ref=nb_sb_noss"

        response, _ = self._get_with_proxy_rotation(search_url)
        if not response:
            return False

        soup = BeautifulSoup(response.content, 'html.parser')

        # 计算搜索结果数量
        results = soup.find_all('div', {'data-component-type': 's-search-result'})

        # 如果搜索结果少于等于5个，认为是唯一品牌
        return len(results) <= 5

    def _extract_reviews_count(self, soup: BeautifulSoup) -> Optional[int]:
        """提取评论数量"""
        reviews_selectors = [
            '#acrCustomerReviewText',
            '.reviewCountTextLinkedHistogram',
            'span[data-hook="total-review-count"]'
        ]

        for selector in reviews_selectors:
            reviews_elem = soup.select_one(selector)
            if reviews_elem:
                reviews_text = reviews_elem.get_text()
                # 提取数字
                numbers = re.findall(r'[\d,]+', reviews_text)
                if numbers:
                    try:
                        return int(numbers[0].replace(',', ''))
                    except ValueError:
                        continue

        return None

    def _extract_sales_rank(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取销售排名"""
        ranks = []

        # 查找销售排名信息
        rank_section = soup.find('div', {'id': 'detailBulletsWrapper_feature_div'})
        if not rank_section:
            rank_section = soup.find('table', {'id': 'productDetails_detailBullets_sections1'})

        if rank_section:
            rank_text = rank_section.get_text()

            # 解析排名信息
            rank_pattern = r'#([\d,]+)\s+in\s+([^(]+)'
            matches = re.findall(rank_pattern, rank_text)

            for rank_str, category in matches:
                try:
                    rank = int(rank_str.replace(',', ''))
                    category = category.strip()
                    ranks.append({'rank': rank, 'category': category})
                except ValueError:
                    continue

        return ranks

    def _extract_price_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取价格信息"""
        price_info = {}

        # 尝试提取当前价格
        price_selectors = [
            '.a-price-whole',
            '.a-offscreen',
            '#price_inside_buybox'
        ]

        for selector in price_selectors:
            price_elem = soup.select_one(selector)
            if price_elem:
                price_text = price_elem.get_text().strip()
                # 提取价格数字
                price_match = re.search(r'[\d,]+\.?\d*', price_text)
                if price_match:
                    try:
                        price_info['current_price'] = float(price_match.group().replace(',', ''))
                        break
                    except ValueError:
                        continue

        return price_info

    def process_asins(self, asins: List[str], progress_callback=None) -> List[Dict[str, Any]]:
        """批量处理ASIN列表"""
        self.total_count = len(asins)
        self.processed_count = 0
        self.results = []

        logger.info(f"开始处理 {len(asins)} 个ASIN")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_asin = {
                executor.submit(self.get_product_info, asin): asin
                for asin in asins
            }

            # 处理完成的任务
            for future in as_completed(future_to_asin):
                asin = future_to_asin[future]
                try:
                    result = future.result()
                    if result:
                        self.results.append(result)
                        logger.info(f"ASIN {asin} 处理完成")
                    else:
                        logger.warning(f"ASIN {asin} 处理失败")

                except Exception as e:
                    logger.error(f"处理ASIN {asin} 时出现异常: {e}")

                self.processed_count += 1

                # 调用进度回调
                if progress_callback:
                    progress_callback(self.processed_count, self.total_count)

        logger.info(f"处理完成，成功处理 {len(self.results)} 个产品")
        return self.results

    def save_results(self, filename: str = 'amazon_results_optimized.xlsx'):
        """保存结果到Excel文件"""
        if not self.results:
            logger.warning("没有结果可保存")
            return

        try:
            df = pd.DataFrame(self.results)
            df.to_excel(filename, index=False)
            logger.info(f"结果已保存到 {filename}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'processed_products': len(self.results),
            'success_rate': len(self.results) / max(self.total_count, 1) * 100,
            'challenge_solve_rate': self.stats['challenges_solved'] / max(self.stats['challenges_detected'], 1) * 100
        }

class ScraperGUI:
    """爬虫GUI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("优化版亚马逊产品爬虫")
        self.root.geometry("800x600")

        self.scraper = None
        self.asins = []

        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(file_frame, text="ASIN文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_var, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)

        # 设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="设置", padding="5")
        settings_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(settings_frame, text="并发线程数:").grid(row=0, column=0, sticky=tk.W)
        self.threads_var = tk.StringVar(value="20")
        ttk.Entry(settings_frame, textvariable=self.threads_var, width=10).grid(row=0, column=1, padx=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, pady=10)

        self.start_button = ttk.Button(control_frame, text="开始抓取", command=self.start_scraping)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(control_frame, text="停止", command=self.stop_scraping, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        self.save_button = ttk.Button(control_frame, text="保存结果", command=self.save_results, state=tk.DISABLED)
        self.save_button.pack(side=tk.LEFT, padx=5)

        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.progress_var = tk.StringVar(value="准备就绪")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack()

        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        self.log_text = tk.Text(log_frame, height=15, width=80)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 统计信息
        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.stats_var = tk.StringVar(value="等待开始...")
        ttk.Label(stats_frame, textvariable=self.stats_var).pack()

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)

    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择ASIN文件",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)

    def load_asins(self, filename: str) -> List[str]:
        """加载ASIN列表"""
        try:
            if filename.endswith('.xlsx'):
                df = pd.read_excel(filename)
                # 假设ASIN在第一列
                asins = df.iloc[:, 0].astype(str).tolist()
            else:
                with open(filename, 'r', encoding='utf-8') as f:
                    asins = [line.strip() for line in f if line.strip()]

            self.log(f"从 {filename} 加载了 {len(asins)} 个ASIN")
            return asins

        except Exception as e:
            self.log(f"加载ASIN文件失败: {e}")
            return []

    def log(self, message: str):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_progress(self, current: int, total: int):
        """更新进度"""
        progress = (current / total) * 100 if total > 0 else 0
        self.progress_bar['value'] = progress
        self.progress_var.set(f"进度: {current}/{total} ({progress:.1f}%)")

        if self.scraper:
            stats = self.scraper.get_stats()
            stats_text = (f"请求: {stats['total_requests']} | "
                         f"成功: {stats['successful_requests']} | "
                         f"失败: {stats['failed_requests']} | "
                         f"验证码: {stats['challenges_detected']}/{stats['challenges_solved']} | "
                         f"成功率: {stats['success_rate']:.1f}%")
            self.stats_var.set(stats_text)

        self.root.update_idletasks()

    def start_scraping(self):
        """开始抓取"""
        if not self.file_var.get():
            messagebox.showerror("错误", "请选择ASIN文件")
            return

        # 加载ASIN
        self.asins = self.load_asins(self.file_var.get())
        if not self.asins:
            messagebox.showerror("错误", "无法加载ASIN文件")
            return

        try:
            max_workers = int(self.threads_var.get())
        except ValueError:
            messagebox.showerror("错误", "线程数必须是数字")
            return

        # 初始化爬虫
        self.scraper = OptimizedAmazonScraper(max_workers=max_workers)

        # 更新UI状态
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.save_button.config(state=tk.DISABLED)

        self.log("开始抓取...")

        # 在新线程中运行抓取
        import threading
        self.scraping_thread = threading.Thread(target=self._run_scraping)
        self.scraping_thread.daemon = True
        self.scraping_thread.start()

    def _run_scraping(self):
        """运行抓取（在后台线程中）"""
        try:
            results = self.scraper.process_asins(self.asins, self.update_progress)

            self.log(f"抓取完成！成功处理 {len(results)} 个产品")

            # 更新UI状态
            self.root.after(0, self._scraping_finished)

        except Exception as e:
            self.log(f"抓取过程中出现错误: {e}")
            self.root.after(0, self._scraping_finished)

    def _scraping_finished(self):
        """抓取完成后的UI更新"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.NORMAL)

    def stop_scraping(self):
        """停止抓取"""
        self.log("正在停止抓取...")
        # 这里可以添加停止逻辑
        self._scraping_finished()

    def save_results(self):
        """保存结果"""
        if not self.scraper or not self.scraper.results:
            messagebox.showwarning("警告", "没有结果可保存")
            return

        filename = filedialog.asksaveasfilename(
            title="保存结果",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if filename:
            self.scraper.save_results(filename)
            self.log(f"结果已保存到 {filename}")
            messagebox.showinfo("成功", f"结果已保存到 {filename}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == '--cli':
        # 命令行模式
        scraper = OptimizedAmazonScraper()

        # 加载ASIN
        asins = []
        if os.path.exists('asin.xlsx'):
            df = pd.read_excel('asin.xlsx')
            asins = df.iloc[:, 0].astype(str).tolist()

        if asins:
            print(f"加载了 {len(asins)} 个ASIN")
            results = scraper.process_asins(asins)
            scraper.save_results()

            stats = scraper.get_stats()
            print(f"处理完成！统计信息: {stats}")
        else:
            print("未找到ASIN文件")
    else:
        # GUI模式
        app = ScraperGUI()
        app.run()

if __name__ == "__main__":
    main()
