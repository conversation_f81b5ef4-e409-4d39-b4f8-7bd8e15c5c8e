#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的手机验证处理补丁
解决多线程和网络超时问题
"""

import time
import logging
import threading
from typing import Optional

logger = logging.getLogger(__name__)

# 全局锁，防止多线程同时使用Selenium
selenium_lock = threading.Lock()

def patch_enhanced_phone_verification():
    """应用增强的手机验证处理补丁"""
    logger.info("🔧 正在应用增强手机验证处理补丁...")
    
    try:
        from 优化版本_amazon_scraper import ChallengeHandler, ChallengeResult, ChallengeType
        
        def enhanced_handle_phone_verification(self, response, soup, start_time):
            """增强的手机验证处理方法"""
            logger.warning("检测到手机验证，开始分析页面内容...")
            
            try:
                # 打印响应内容用于分析
                self._debug_phone_verification_page(response, soup)
                
                # 尝试破解手机验证，增加重试机制
                max_attempts = 3
                for attempt in range(max_attempts):
                    try:
                        logger.info(f"🔄 手机验证破解尝试 {attempt + 1}/{max_attempts}")
                        
                        if self._attempt_phone_verification_bypass_enhanced(response, soup, attempt):
                            logger.info("🎉 手机验证破解成功！")
                            return ChallengeResult(
                                challenge_type=ChallengeType.PHONE_VERIFICATION,
                                solved=True,
                                method_used="phone_verification_bypass_enhanced",
                                time_taken=time.time() - start_time
                            )
                    except Exception as e:
                        logger.warning(f"⚠️ 第{attempt + 1}次破解尝试失败: {e}")
                        if attempt < max_attempts - 1:
                            time.sleep(2)  # 等待2秒后重试
                        continue
                
                # 如果所有尝试都失败，标记代理需要冷却
                logger.warning("❌ 所有手机验证破解尝试都失败，将当前代理标记为需要长时间冷却")
                current_proxy = getattr(self.session, '_current_proxy', None)
                if current_proxy:
                    # 设置长时间冷却（30分钟）
                    self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=1800)
                    logger.info(f"🕒 代理 {current_proxy.host}:{current_proxy.port} 已设置30分钟冷却")
                
                return ChallengeResult(
                    challenge_type=ChallengeType.PHONE_VERIFICATION,
                    solved=False,
                    method_used="proxy_cooling",
                    time_taken=time.time() - start_time,
                    error_message="手机验证需要更换代理"
                )
                
            except Exception as e:
                logger.error(f"❌ 手机验证处理过程中出现异常: {e}")
                return ChallengeResult(
                    challenge_type=ChallengeType.PHONE_VERIFICATION,
                    solved=False,
                    method_used="exception_handling",
                    time_taken=time.time() - start_time,
                    error_message=f"处理异常: {str(e)}"
                )
        
        def enhanced_attempt_phone_verification_bypass(self, response, soup, attempt_number=0):
            """增强的手机验证破解尝试方法"""
            logger.info(f"🚀 开始增强手机验证破解尝试 (第{attempt_number + 1}次)...")
            
            try:
                # 策略1: 查找跳过链接（快速策略）
                if attempt_number == 0:
                    logger.info("📝 策略1: 查找跳过链接...")
                    if self._try_skip_phone_verification_enhanced(response, soup):
                        return True
                
                # 策略2: 尝试虚假手机号（中等策略）
                if attempt_number <= 1:
                    logger.info("📱 策略2: 尝试使用虚假手机号...")
                    if self._try_fake_phone_number_enhanced(response, soup):
                        return True
                
                # 策略3: 尝试返回上一页
                logger.info("🔙 策略3: 尝试返回上一页...")
                if self._try_go_back_enhanced(response):
                    return True
                
                # 策略4: 尝试直接访问产品页面
                logger.info("🎯 策略4: 尝试直接访问产品页面...")
                if self._try_direct_product_access_enhanced(response):
                    return True
                
                # 策略5: 使用Selenium进行高级绕过（最后的策略，使用锁防止冲突）
                logger.info("🔧 策略5: 使用Selenium进行高级绕过...")
                with selenium_lock:
                    if self._use_selenium_bypass_enhanced(response, soup, attempt_number):
                        return True
                
                return False
                
            except Exception as e:
                logger.error(f"❌ 增强破解尝试失败: {e}")
                return False
        
        def enhanced_try_skip_phone_verification(self, response, soup):
            """增强的跳过链接策略"""
            try:
                # 查找各种可能的跳过链接
                skip_patterns = [
                    'skip', 'Skip', 'SKIP',
                    'later', 'Later', 'LATER',
                    'not now', 'Not now', 'NOT NOW',
                    'maybe later', 'Maybe later',
                    'ignoreAuthState=1'
                ]
                
                skip_links = []
                for pattern in skip_patterns:
                    links = soup.find_all('a', string=lambda text: text and pattern in text)
                    skip_links.extend(links)
                    
                    # 也查找href中包含跳过模式的链接
                    href_links = soup.find_all('a', href=lambda href: href and pattern.lower() in href.lower())
                    skip_links.extend(href_links)
                
                for link in skip_links:
                    href = link.get('href')
                    if href:
                        try:
                            if not href.startswith('http'):
                                from urllib.parse import urljoin
                                href = urljoin(response.url, href)
                            
                            logger.info(f"🔗 找到跳过链接: {href}")
                            
                            # 点击跳过链接，增加超时控制
                            skip_response = self.session.get(href, timeout=10)
                            if skip_response.status_code == 200:
                                # 检查是否成功跳过
                                if ('phone' not in skip_response.url.lower() and 
                                    'verification' not in skip_response.url.lower() and
                                    'signin' not in skip_response.url.lower()):
                                    logger.info("✅ 成功通过跳过链接绕过手机验证")
                                    return True
                        except Exception as e:
                            logger.warning(f"⚠️ 点击跳过链接失败: {e}")
                            continue
                
                return False
                
            except Exception as e:
                logger.error(f"❌ 跳过链接策略失败: {e}")
                return False
        
        def enhanced_try_fake_phone_number(self, response, soup):
            """增强的虚假手机号策略"""
            try:
                # 查找表单
                form = soup.find('form')
                if not form:
                    logger.warning("⚠️ 未找到表单")
                    return False
                
                action = form.get('action', '')
                if not action.startswith('http'):
                    from urllib.parse import urljoin
                    action = urljoin(response.url, action)
                
                # 构建表单数据
                form_data = {}
                
                # 获取所有隐藏字段
                for hidden_input in form.find_all('input', {'type': 'hidden'}):
                    name = hidden_input.get('name')
                    value = hidden_input.get('value', '')
                    if name:
                        form_data[name] = value
                
                # 查找手机号输入框
                phone_input = form.find('input', {'type': ['tel', 'text']})
                if not phone_input:
                    import re
                    phone_input = form.find('input', {'name': re.compile(r'phone|mobile', re.I)})
                
                if phone_input:
                    phone_field_name = phone_input.get('name', 'phoneNumber')
                    
                    # 尝试多个虚假手机号
                    fake_numbers = [
                        '******-0123',
                        '555-0123', 
                        '1234567890',
                        '******-555-0199',
                        '(*************'
                    ]
                    
                    for fake_number in fake_numbers:
                        try:
                            form_data[phone_field_name] = fake_number
                            logger.info(f"📱 尝试提交虚假手机号: {fake_number}")
                            
                            # 提交表单，增加超时控制
                            submit_response = self.session.post(action, data=form_data, timeout=15)
                            
                            if submit_response.status_code == 200:
                                # 检查是否成功绕过
                                if ('phone' not in submit_response.url.lower() and 
                                    'verification' not in submit_response.url.lower()):
                                    logger.info(f"✅ 虚假手机号 {fake_number} 绕过成功")
                                    return True
                                    
                        except Exception as e:
                            logger.warning(f"⚠️ 提交虚假手机号 {fake_number} 失败: {e}")
                            continue
                
                return False
                
            except Exception as e:
                logger.error(f"❌ 虚假手机号策略失败: {e}")
                return False
        
        def enhanced_use_selenium_bypass(self, response, soup, attempt_number):
            """增强的Selenium绕过策略"""
            try:
                logger.info(f"🔧 启动Selenium绕过 (尝试 {attempt_number + 1})...")
                
                # 设置更短的超时时间，避免长时间阻塞
                timeout = max(30 - attempt_number * 5, 15)  # 递减超时时间
                
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.common.exceptions import TimeoutException, WebDriverException
                
                chrome_options = Options()
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--window-size=1920,1080')
                chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
                
                driver = None
                try:
                    driver = webdriver.Chrome(options=chrome_options)
                    driver.set_page_load_timeout(timeout)
                    
                    # 访问页面
                    driver.get(response.url)
                    
                    # 等待页面加载
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    
                    # 尝试多种JavaScript绕过方法
                    bypass_scripts = [
                        # 方法1: 直接跳转
                        "window.location.href = 'https://www.amazon.com/';",
                        
                        # 方法2: 模拟点击跳过
                        """
                        var skipLinks = document.querySelectorAll('a[href*="skip"], a[href*="ignoreAuthState"]');
                        if (skipLinks.length > 0) {
                            skipLinks[0].click();
                            return true;
                        }
                        return false;
                        """,
                        
                        # 方法3: 提交空表单
                        """
                        var forms = document.querySelectorAll('form');
                        if (forms.length > 0) {
                            var phoneInputs = forms[0].querySelectorAll('input[type="tel"], input[name*="phone"]');
                            if (phoneInputs.length > 0) {
                                phoneInputs[0].value = '555-0123';
                                forms[0].submit();
                                return true;
                            }
                        }
                        return false;
                        """,
                        
                        # 方法4: 历史回退
                        "window.history.back(); return true;"
                    ]
                    
                    for i, script in enumerate(bypass_scripts):
                        try:
                            logger.info(f"🔧 执行JavaScript方法 {i + 1}...")
                            result = driver.execute_script(script)
                            
                            # 等待页面变化
                            time.sleep(2)
                            
                            current_url = driver.current_url
                            if ('phone' not in current_url.lower() and 
                                'verification' not in current_url.lower()):
                                logger.info(f"✅ JavaScript方法 {i + 1} 绕过成功")
                                return True
                                
                        except Exception as e:
                            logger.warning(f"⚠️ JavaScript方法 {i + 1} 失败: {e}")
                            continue
                    
                    return False
                    
                except TimeoutException:
                    logger.warning(f"⏰ Selenium操作超时 ({timeout}秒)")
                    return False
                except WebDriverException as e:
                    logger.warning(f"⚠️ Selenium WebDriver错误: {e}")
                    return False
                finally:
                    if driver:
                        try:
                            driver.quit()
                        except:
                            pass
                            
            except Exception as e:
                logger.error(f"❌ Selenium绕过策略失败: {e}")
                return False
        
        # 应用补丁
        ChallengeHandler._handle_phone_verification = enhanced_handle_phone_verification
        ChallengeHandler._attempt_phone_verification_bypass_enhanced = enhanced_attempt_phone_verification_bypass
        ChallengeHandler._try_skip_phone_verification_enhanced = enhanced_try_skip_phone_verification
        ChallengeHandler._try_fake_phone_number_enhanced = enhanced_try_fake_phone_number
        ChallengeHandler._use_selenium_bypass_enhanced = enhanced_use_selenium_bypass
        
        # 添加简单的回退和直接访问方法
        def enhanced_try_go_back(self, response):
            """尝试返回上一页"""
            try:
                # 构造一个可能的返回URL
                base_url = "https://www.amazon.com/"
                back_response = self.session.get(base_url, timeout=10)
                return back_response.status_code == 200
            except:
                return False
        
        def enhanced_try_direct_product_access(self, response):
            """尝试直接访问产品页面"""
            try:
                # 如果有产品ASIN，尝试直接访问
                import re
                asin_match = re.search(r'/dp/([A-Z0-9]{10})', response.url)
                if asin_match:
                    asin = asin_match.group(1)
                    product_url = f"https://www.amazon.com/dp/{asin}"
                    product_response = self.session.get(product_url, timeout=10)
                    return product_response.status_code == 200
                return False
            except:
                return False
        
        ChallengeHandler._try_go_back_enhanced = enhanced_try_go_back
        ChallengeHandler._try_direct_product_access_enhanced = enhanced_try_direct_product_access
        
        logger.info("✅ 增强手机验证处理补丁应用成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 应用增强补丁失败: {e}")
        return False

if __name__ == "__main__":
    # 应用补丁
    if patch_enhanced_phone_verification():
        print("🎉 增强手机验证处理补丁应用成功！")
        print("现在可以运行优化版本的爬虫了。")
    else:
        print("❌ 补丁应用失败，请检查错误信息。")
