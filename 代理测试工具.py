#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理测试工具 - 检查代理IP地理位置和Amazon访问效果
"""

import requests
import time
import json
from urllib.parse import urlparse

def test_proxy_location(proxy_url):
    """测试代理的地理位置"""
    try:
        # 解析代理URL
        parsed = urlparse(proxy_url)
        if parsed.scheme == 'socks5':
            proxy_dict = {
                'http': proxy_url,
                'https': proxy_url
            }
        else:
            proxy_dict = {
                'http': proxy_url,
                'https': proxy_url
            }
        
        print(f"🔍 测试代理: {proxy_url}")
        
        # 测试IP地理位置
        print("   📍 检查IP地理位置...")
        response = requests.get('https://ipapi.co/json/', proxies=proxy_dict, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            country = data.get('country_code', 'Unknown')
            country_name = data.get('country_name', 'Unknown')
            city = data.get('city', 'Unknown')
            region = data.get('region', 'Unknown')
            ip = data.get('ip', 'Unknown')
            
            print(f"   ✅ IP地址: {ip}")
            print(f"   🌍 国家: {country_name} ({country})")
            print(f"   🏙️  城市: {city}, {region}")
            
            if country == 'US':
                print(f"   ✅ 美国IP - 适合Amazon爬虫")
                return True, data
            else:
                print(f"   ⚠️  非美国IP - 可能导致中文页面")
                return False, data
        else:
            print(f"   ❌ 无法获取IP信息，状态码: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 代理测试失败: {str(e)}")
        return False, None

def test_amazon_access(proxy_url):
    """测试通过代理访问Amazon的效果"""
    try:
        # 解析代理URL
        parsed = urlparse(proxy_url)
        if parsed.scheme == 'socks5':
            proxy_dict = {
                'http': proxy_url,
                'https': proxy_url
            }
        else:
            proxy_dict = {
                'http': proxy_url,
                'https': proxy_url
            }
        
        print("   🛒 测试Amazon访问...")
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        }
        
        # 访问Amazon首页
        start_time = time.time()
        response = requests.get('https://www.amazon.com/', proxies=proxy_dict, headers=headers, timeout=15)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"   ✅ Amazon访问成功，响应时间: {response_time:.2f}秒")
            
            # 检查页面语言
            content = response.text.lower()
            if '中文' in content or '亚马逊' in content:
                print(f"   ⚠️  检测到中文内容，可能显示中文页面")
                return False, response_time
            else:
                print(f"   ✅ 页面为英文，适合爬虫使用")
                return True, response_time
        else:
            print(f"   ❌ Amazon访问失败，状态码: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Amazon访问测试失败: {str(e)}")
        return False, None

def test_proxy_file(proxy_file='proxies.txt'):
    """测试代理文件中的所有代理"""
    print("🧪 代理文件测试工具")
    print("=" * 50)
    
    try:
        with open(proxy_file, 'r', encoding='utf-8') as f:
            proxies = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        if not proxies:
            print(f"❌ 代理文件 {proxy_file} 为空或不存在")
            return
        
        print(f"📋 找到 {len(proxies)} 个代理，开始测试...")
        print()
        
        results = []
        
        for i, proxy in enumerate(proxies, 1):
            print(f"🔍 [{i}/{len(proxies)}] 测试代理 {i}")
            
            # 测试地理位置
            is_us, location_data = test_proxy_location(proxy)
            
            # 测试Amazon访问
            amazon_ok, response_time = test_amazon_access(proxy)
            
            # 记录结果
            result = {
                'proxy': proxy,
                'is_us': is_us,
                'amazon_ok': amazon_ok,
                'response_time': response_time,
                'location_data': location_data
            }
            results.append(result)
            
            print()
            time.sleep(1)  # 避免请求过快
        
        # 显示测试总结
        print("🎯 测试总结")
        print("=" * 50)
        
        us_proxies = [r for r in results if r['is_us']]
        amazon_ok_proxies = [r for r in results if r['amazon_ok']]
        good_proxies = [r for r in results if r['is_us'] and r['amazon_ok']]
        
        print(f"📊 统计结果:")
        print(f"   总代理数: {len(results)}")
        print(f"   美国IP代理: {len(us_proxies)}")
        print(f"   Amazon访问正常: {len(amazon_ok_proxies)}")
        print(f"   推荐使用代理: {len(good_proxies)}")
        
        if good_proxies:
            print(f"\n✅ 推荐使用的代理:")
            for i, result in enumerate(good_proxies, 1):
                location = result['location_data']
                city = location.get('city', 'Unknown') if location else 'Unknown'
                response_time = result['response_time']
                print(f"   {i}. {result['proxy']}")
                print(f"      位置: {city}, 响应时间: {response_time:.2f}秒")
        else:
            print(f"\n❌ 没有找到合适的代理")
            print(f"   建议:")
            print(f"   1. 检查代理配置是否正确")
            print(f"   2. 确保代理支持HTTPS流量")
            print(f"   3. 考虑更换代理服务商")
        
        return results
        
    except FileNotFoundError:
        print(f"❌ 代理文件 {proxy_file} 不存在")
        print(f"请创建代理文件，格式如下:")
        print(f"socks5://username:password@proxy-ip:port")
        return None
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return None

def test_single_proxy():
    """测试单个代理"""
    print("🧪 单个代理测试")
    print("=" * 30)
    
    proxy_url = input("请输入代理URL (格式: socks5://user:pass@ip:port): ").strip()
    
    if not proxy_url:
        print("❌ 代理URL不能为空")
        return
    
    print()
    is_us, location_data = test_proxy_location(proxy_url)
    amazon_ok, response_time = test_amazon_access(proxy_url)
    
    print("\n🎯 测试结果:")
    if is_us and amazon_ok:
        print("✅ 代理测试通过，推荐使用")
    elif is_us:
        print("⚠️ 代理是美国IP但Amazon访问有问题")
    elif amazon_ok:
        print("⚠️ Amazon访问正常但不是美国IP，可能出现中文页面")
    else:
        print("❌ 代理测试失败，不推荐使用")

def main():
    """主函数"""
    print("🛠️ Amazon爬虫代理测试工具")
    print("=" * 40)
    print("1. 测试代理文件中的所有代理")
    print("2. 测试单个代理")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == '1':
            test_proxy_file()
        elif choice == '2':
            test_single_proxy()
        elif choice == '3':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == "__main__":
    main()
