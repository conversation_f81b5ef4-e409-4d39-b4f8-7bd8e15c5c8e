#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复手机验证问题的补丁
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def patch_challenge_handler():
    """为ChallengeHandler类添加手机验证处理方法"""
    
    # 导入需要的模块
    from 优化版本_amazon_scraper import ChallengeHandler, ChallengeResult, ChallengeType
    import time
    import logging
    
    logger = logging.getLogger(__name__)
    
    def _handle_phone_verification(self, response, soup, start_time):
        """处理手机验证 - 快速修复版本"""
        logger.warning("检测到手机验证，将当前代理标记为需要长时间冷却")
        
        # 获取当前代理并标记为需要冷却
        current_proxy = getattr(self.session, '_current_proxy', None)
        if current_proxy and hasattr(self.proxy_manager, 'mark_proxy_cooling'):
            # 设置长时间冷却（30分钟）
            self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=1800)
            logger.info(f"代理 {current_proxy.host}:{current_proxy.port} 已设置30分钟冷却")
        elif current_proxy:
            # 如果没有mark_proxy_cooling方法，使用mark_proxy_failed
            self.proxy_manager.mark_proxy_failed(current_proxy, ban_duration=1800)
            logger.info(f"代理 {current_proxy.host}:{current_proxy.port} 已标记为失败（30分钟）")
        
        return ChallengeResult(
            challenge_type=ChallengeType.PHONE_VERIFICATION,
            solved=False,
            method_used="proxy_cooling",
            time_taken=time.time() - start_time,
            error_message="手机验证需要更换代理"
        )
    
    def _handle_email_verification(self, response, soup, start_time):
        """处理邮箱验证 - 快速修复版本"""
        logger.warning("检测到邮箱验证，将当前代理标记为需要冷却")
        
        # 获取当前代理并标记为需要冷却
        current_proxy = getattr(self.session, '_current_proxy', None)
        if current_proxy and hasattr(self.proxy_manager, 'mark_proxy_cooling'):
            # 设置中等时间冷却（15分钟）
            self.proxy_manager.mark_proxy_cooling(current_proxy, cooling_time=900)
            logger.info(f"代理 {current_proxy.host}:{current_proxy.port} 已设置15分钟冷却")
        elif current_proxy:
            # 如果没有mark_proxy_cooling方法，使用mark_proxy_failed
            self.proxy_manager.mark_proxy_failed(current_proxy, ban_duration=900)
            logger.info(f"代理 {current_proxy.host}:{current_proxy.port} 已标记为失败（15分钟）")
        
        return ChallengeResult(
            challenge_type=ChallengeType.EMAIL_VERIFICATION,
            solved=False,
            method_used="proxy_cooling",
            time_taken=time.time() - start_time,
            error_message="邮箱验证需要更换代理"
        )
    
    # 动态添加方法到ChallengeHandler类
    ChallengeHandler._handle_phone_verification = _handle_phone_verification
    ChallengeHandler._handle_email_verification = _handle_email_verification
    
    # 修改handle_challenge方法
    original_handle_challenge = ChallengeHandler.handle_challenge
    
    def patched_handle_challenge(self, challenge_type, response, soup):
        """修补后的handle_challenge方法"""
        start_time = time.time()
        
        try:
            if challenge_type == ChallengeType.IMAGE_CAPTCHA:
                return self._handle_image_captcha(response, soup, start_time)
            elif challenge_type == ChallengeType.CLICK_CONTINUE:
                return self._handle_click_continue(response, soup, start_time)
            elif challenge_type == ChallengeType.ROBOT_CHECK:
                return self._handle_robot_check(response, soup, start_time)
            elif challenge_type == ChallengeType.PHONE_VERIFICATION:
                return self._handle_phone_verification(response, soup, start_time)
            elif challenge_type == ChallengeType.EMAIL_VERIFICATION:
                return self._handle_email_verification(response, soup, start_time)
            else:
                return ChallengeResult(
                    challenge_type=challenge_type,
                    solved=False,
                    method_used="unsupported",
                    time_taken=time.time() - start_time,
                    error_message=f"不支持的验证类型: {challenge_type}"
                )
        except Exception as e:
            return ChallengeResult(
                challenge_type=challenge_type,
                solved=False,
                method_used="error",
                time_taken=time.time() - start_time,
                error_message=str(e)
            )
    
    # 替换原方法
    ChallengeHandler.handle_challenge = patched_handle_challenge
    
    print("✅ 手机验证处理补丁已应用！")
    print("现在可以正确处理PHONE_VERIFICATION和EMAIL_VERIFICATION挑战")

def patch_proxy_manager():
    """为ProxyManager类添加mark_proxy_cooling方法"""
    
    from 优化版本_amazon_scraper import ProxyManager, ProxyStatus
    import time
    import logging
    
    logger = logging.getLogger(__name__)
    
    def mark_proxy_cooling(self, proxy, cooling_time=900):
        """标记代理需要冷却"""
        with self.proxy_lock:
            proxy.status = ProxyStatus.COOLING
            proxy.cooling_until = time.time() + cooling_time
            logger.info(f"代理设置冷却: {proxy.url}, 冷却时间: {cooling_time}秒")
    
    # 动态添加方法到ProxyManager类
    ProxyManager.mark_proxy_cooling = mark_proxy_cooling
    
    print("✅ 代理管理器冷却功能补丁已应用！")

def apply_patches():
    """应用所有补丁"""
    print("🔧 正在应用快速修复补丁...")
    
    try:
        patch_proxy_manager()
        patch_challenge_handler()
        print("🎉 所有补丁应用成功！")
        print("现在优化版本可以正确处理手机验证和邮箱验证挑战了。")
        return True
    except Exception as e:
        print(f"❌ 补丁应用失败: {e}")
        return False

if __name__ == "__main__":
    # 直接运行此文件来应用补丁
    apply_patches()
