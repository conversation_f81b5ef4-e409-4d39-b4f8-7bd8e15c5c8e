#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Amazon验证页面并使用MCP pcm工具进行逆向分析
"""

import requests
import json
import time
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_proxy():
    """加载一个代理"""
    try:
        with open('proxies.txt', 'r', encoding='utf-8') as f:
            content = f.read()
            proxy_blocks = content.split('\n\n')
            
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                proxy_info = {}
                for line in block.strip().split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        proxy_info[key.strip()] = value.strip()
                
                if all(k in proxy_info for k in ['地址', '端口', '用户名', '密码']):
                    proxy_url = f"socks5://{proxy_info['用户名']}:{proxy_info['密码']}@{proxy_info['地址']}:{proxy_info['端口']}"
                    return {
                        'http': proxy_url,
                        'https': proxy_url
                    }
        return None
    except Exception as e:
        logger.error(f"加载代理失败: {e}")
        return None

def get_verification_page():
    """获取验证页面进行分析"""
    proxy = load_proxy()
    if not proxy:
        logger.error("无法加载代理")
        return None
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    session = requests.Session()
    session.proxies = proxy
    session.headers.update(headers)
    
    # 尝试访问一个ASIN直到遇到验证页面
    test_asins = ["B097XQ49D4", "B07G3KS4JK", "B009OFW11I"]
    
    for asin in test_asins:
        try:
            url = f"https://www.amazon.com/dp/{asin}"
            logger.info(f"🔍 尝试访问 {url}")
            
            response = session.get(url, timeout=30)
            logger.info(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查是否是验证页面
                if "Click the button below to continue shopping" in response.text:
                    logger.info("🎯 找到验证页面！")
                    
                    # 保存完整响应
                    with open(f'verification_analysis_{asin}.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    # 保存响应信息
                    response_info = {
                        'url': response.url,
                        'status_code': response.status_code,
                        'headers': dict(response.headers),
                        'cookies': dict(response.cookies),
                        'history': [r.url for r in response.history]
                    }
                    
                    with open(f'verification_info_{asin}.json', 'w', encoding='utf-8') as f:
                        json.dump(response_info, f, indent=2, ensure_ascii=False)
                    
                    # 解析页面结构
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    logger.info("📋 页面分析:")
                    logger.info(f"   标题: {soup.title.string if soup.title else 'N/A'}")
                    
                    # 分析表单
                    forms = soup.find_all('form')
                    logger.info(f"   表单数量: {len(forms)}")
                    
                    for i, form in enumerate(forms):
                        logger.info(f"   表单 {i+1}:")
                        logger.info(f"     Action: {form.get('action', 'N/A')}")
                        logger.info(f"     Method: {form.get('method', 'N/A')}")
                        
                        inputs = form.find_all('input')
                        for inp in inputs:
                            logger.info(f"     Input: {inp.get('name', 'N/A')} = {inp.get('value', 'N/A')} (type: {inp.get('type', 'N/A')})")
                    
                    # 分析脚本
                    scripts = soup.find_all('script')
                    logger.info(f"   脚本数量: {len(scripts)}")
                    
                    for i, script in enumerate(scripts):
                        if script.string:
                            script_content = script.string.strip()
                            if len(script_content) > 50:  # 只显示有意义的脚本
                                logger.info(f"   脚本 {i+1} (前100字符): {script_content[:100]}...")
                    
                    # 分析链接
                    links = soup.find_all('a', href=True)
                    logger.info(f"   链接数量: {len(links)}")
                    
                    for link in links:
                        href = link.get('href')
                        text = link.get_text(strip=True)
                        if text and len(text) > 0:
                            logger.info(f"   链接: {text} -> {href}")
                    
                    return {
                        'asin': asin,
                        'response': response,
                        'soup': soup,
                        'file_path': f'verification_analysis_{asin}.html'
                    }
                
                elif "productTitle" in response.text:
                    logger.info("✅ 这是产品页面，继续尝试下一个ASIN")
                    continue
                else:
                    logger.info("❓ 未知页面类型")
                    continue
            
        except Exception as e:
            logger.error(f"访问 {asin} 时出错: {e}")
            continue
    
    logger.warning("未找到验证页面")
    return None

def main():
    """主函数"""
    logger.info("🔍 开始分析Amazon验证页面...")
    
    result = get_verification_page()
    
    if result:
        logger.info(f"✅ 成功获取验证页面: {result['asin']}")
        logger.info(f"📁 详细信息已保存到: {result['file_path']}")
        
        # 现在可以使用MCP pcm工具进行进一步分析
        logger.info("🛠️ 准备使用MCP pcm工具进行逆向分析...")
        logger.info("📋 建议的分析步骤:")
        logger.info("   1. 使用pcm工具分析HTML结构")
        logger.info("   2. 查找JavaScript中的绕过逻辑")
        logger.info("   3. 分析表单提交机制")
        logger.info("   4. 寻找隐藏的API端点")
        
        return result
    else:
        logger.error("❌ 未能获取验证页面")
        return None

if __name__ == "__main__":
    main()
