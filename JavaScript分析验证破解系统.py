#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于JavaScript分析的Amazon验证破解系统
通过分析验证页面的JavaScript代码来寻找绕过机会
"""

import requests
import time
import random
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs
import logging

logger = logging.getLogger(__name__)

class JavaScriptAnalysisVerificationBypass:
    """基于JavaScript分析的验证破解系统"""
    
    def __init__(self, session):
        self.session = session
        self.bypass_stats = {
            'js_analysis': 0,
            'token_extraction': 0,
            'cookie_manipulation': 0,
            'header_spoofing': 0,
            'timing_bypass': 0
        }
        
        # 从HTML分析中提取的关键JavaScript模式
        self.js_patterns = {
            'csrf_token': [
                r'csrf[_-]?token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'_token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'authenticity_token["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ],
            'session_id': [
                r'session[_-]?id["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'sid["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ],
            'validation_keys': [
                r'amzn["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'field[_-]?keywords["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'validateCaptcha["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ],
            'timing_functions': [
                r'setTimeout\s*\(\s*function\s*\(\s*\)\s*\{([^}]+)\}',
                r'setInterval\s*\(\s*function\s*\(\s*\)\s*\{([^}]+)\}',
                r'requestAnimationFrame\s*\(\s*function\s*\(\s*\)\s*\{([^}]+)\}'
            ],
            'event_handlers': [
                r'addEventListener\s*\(\s*["\']([^"\']+)["\']',
                r'on([a-z]+)\s*=\s*function',
                r'\.on\s*\(\s*["\']([^"\']+)["\']'
            ]
        }
    
    def analyze_javascript_code(self, html_content):
        """深度分析JavaScript代码"""
        analysis = {
            'tokens': {},
            'functions': [],
            'events': [],
            'timing': [],
            'bypass_opportunities': []
        }
        
        # 提取所有script标签内容
        soup = BeautifulSoup(html_content, 'html.parser')
        scripts = soup.find_all('script')
        
        all_js_code = ""
        for script in scripts:
            if script.string:
                all_js_code += script.string + "\n"
        
        logger.info(f"🔍 提取到 {len(scripts)} 个script标签，总代码长度: {len(all_js_code)}")
        
        # 分析各种模式
        for pattern_type, patterns in self.js_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, all_js_code, re.IGNORECASE | re.MULTILINE)
                if matches:
                    if pattern_type not in analysis['tokens']:
                        analysis['tokens'][pattern_type] = []
                    analysis['tokens'][pattern_type].extend(matches)
                    logger.info(f"🔍 发现 {pattern_type}: {matches[:3]}...")  # 只显示前3个
        
        # 寻找特定的Amazon验证相关代码
        amazon_patterns = [
            r'validateCaptcha',
            r'errors/validateCaptcha',
            r'amzn-r',
            r'field-keywords',
            r'continue shopping',
            r'robot check',
            r'ue_id',
            r'ue_sid'
        ]
        
        for pattern in amazon_patterns:
            if re.search(pattern, all_js_code, re.IGNORECASE):
                analysis['bypass_opportunities'].append(f'amazon_{pattern.replace(" ", "_")}')
        
        return analysis
    
    def extract_dynamic_tokens(self, response, soup):
        """提取动态生成的token"""
        tokens = {}
        
        try:
            # 从JavaScript中提取token
            js_analysis = self.analyze_javascript_code(response.text)
            
            # 合并所有找到的token
            for token_type, token_list in js_analysis['tokens'].items():
                if token_list:
                    tokens[token_type] = token_list[0]  # 使用第一个找到的token
            
            # 从页面元素中提取token
            meta_tokens = soup.find_all('meta', attrs={'name': re.compile(r'.*token.*', re.I)})
            for meta in meta_tokens:
                content = meta.get('content')
                if content:
                    tokens[f"meta_{meta.get('name', 'unknown')}"] = content
            
            # 从隐藏输入字段提取token
            hidden_inputs = soup.find_all('input', type='hidden')
            for inp in hidden_inputs:
                name = inp.get('name', '')
                value = inp.get('value', '')
                if name and value and any(keyword in name.lower() for keyword in ['token', 'csrf', 'auth', 'amzn']):
                    tokens[f"input_{name}"] = value
            
            logger.info(f"🔍 提取到动态token: {list(tokens.keys())}")
            return tokens
            
        except Exception as e:
            logger.error(f"提取动态token时出错: {e}")
            return {}
    
    def generate_realistic_headers(self, base_headers=None):
        """生成更真实的请求头"""
        headers = base_headers.copy() if base_headers else {}
        
        # 添加更多真实的浏览器头
        realistic_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        headers.update(realistic_headers)
        return headers
    
    def bypass_with_js_analysis(self, response, soup, analysis):
        """基于JavaScript分析的高级绕过"""
        logger.info("🚀 开始基于JavaScript分析的验证绕过...")
        
        # 策略1: Token注入绕过
        if analysis['tokens']:
            try:
                logger.info("🔧 尝试Token注入绕过...")
                
                # 提取动态token
                dynamic_tokens = self.extract_dynamic_tokens(response, soup)
                
                # 构造带有所有token的请求
                bypass_data = {}
                bypass_data.update(dynamic_tokens)
                
                # 添加从表单中提取的数据
                forms = soup.find_all('form')
                if forms:
                    form = forms[0]
                    for inp in form.find_all('input'):
                        name = inp.get('name')
                        value = inp.get('value', '')
                        if name:
                            bypass_data[name] = value
                
                # 发送绕过请求
                action_url = urljoin(response.url, form.get('action', '/errors/validateCaptcha'))
                headers = self.generate_realistic_headers(dict(self.session.headers))
                
                logger.info(f"🔧 Token注入请求到: {action_url}")
                logger.info(f"🔧 注入数据: {list(bypass_data.keys())}")
                
                bypass_response = self.session.get(action_url, params=bypass_data, headers=headers, timeout=30)
                
                if bypass_response.status_code == 200 and self._is_product_page(bypass_response.text):
                    logger.info("✅ Token注入绕过成功！")
                    self.bypass_stats['token_extraction'] += 1
                    return bypass_response
                    
            except Exception as e:
                logger.error(f"Token注入绕过失败: {e}")
        
        # 策略2: 时序绕过
        if 'amazon_continue_shopping' in analysis['bypass_opportunities']:
            try:
                logger.info("🔧 尝试时序绕过...")
                
                # 模拟人类行为的延迟
                human_delay = random.uniform(2.5, 4.5)
                logger.info(f"🔧 模拟人类延迟: {human_delay:.2f}秒")
                time.sleep(human_delay)
                
                # 使用更真实的头部重新请求
                headers = self.generate_realistic_headers(dict(self.session.headers))
                headers['Referer'] = response.url
                
                timing_response = self.session.get(response.url, headers=headers, timeout=30)
                
                if timing_response.status_code == 200 and self._is_product_page(timing_response.text):
                    logger.info("✅ 时序绕过成功！")
                    self.bypass_stats['timing_bypass'] += 1
                    return timing_response
                    
            except Exception as e:
                logger.error(f"时序绕过失败: {e}")
        
        # 策略3: Cookie操作绕过
        try:
            logger.info("🔧 尝试Cookie操作绕过...")
            
            # 添加一些常见的绕过cookie
            bypass_cookies = {
                'robot_check': 'passed',
                'captcha_solved': '1',
                'human_verified': 'true',
                'validation_bypass': str(int(time.time()))
            }
            
            # 保存原始cookies
            original_cookies = dict(self.session.cookies)
            
            # 添加绕过cookies
            for name, value in bypass_cookies.items():
                self.session.cookies[name] = value
            
            headers = self.generate_realistic_headers(dict(self.session.headers))
            cookie_response = self.session.get(response.url, headers=headers, timeout=30)
            
            if cookie_response.status_code == 200 and self._is_product_page(cookie_response.text):
                logger.info("✅ Cookie操作绕过成功！")
                self.bypass_stats['cookie_manipulation'] += 1
                return cookie_response
            else:
                # 恢复原始cookies
                self.session.cookies.clear()
                self.session.cookies.update(original_cookies)
                
        except Exception as e:
            logger.error(f"Cookie操作绕过失败: {e}")
        
        logger.warning("❌ 所有JavaScript分析绕过策略都失败了")
        return None
    
    def _is_product_page(self, html_content):
        """检查是否是产品页面"""
        product_indicators = [
            "productTitle",
            "product-title", 
            "dp-container",
            "feature-bullets",
            "bylineInfo",
            "price",
            "availability"
        ]
        
        for indicator in product_indicators:
            if indicator in html_content:
                return True
        
        return False
    
    def get_bypass_stats(self):
        """获取绕过统计"""
        return self.bypass_stats.copy()
