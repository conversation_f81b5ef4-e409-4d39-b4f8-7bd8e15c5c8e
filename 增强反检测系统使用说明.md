# Amazon产品筛选器 - 增强反检测版本使用说明

## 🚀 主要改进功能

### 1. Cookie池管理系统
- **问题解决**: 原版本每次遇到中文页面都要启动Selenium更新Cookie，效率低下
- **改进方案**: 
  - 预先获取并维护多个有效Cookie
  - 智能轮换使用，减少Selenium启动频率
  - 自动监控Cookie有效性，及时补充失效Cookie
  - Cookie成功率统计，优先使用高成功率Cookie

### 2. 智能反检测机制
- **浏览器指纹轮换**: 自动生成和轮换多种浏览器指纹
- **请求头随机化**: 动态生成真实的浏览器请求头
- **会话管理**: 定期刷新会话，避免长时间使用同一会话被检测
- **TLS指纹伪装**: 模拟真实浏览器的TLS握手特征

### 3. 自适应延迟控制
- **智能延迟**: 基于成功/失败率动态调整请求间隔
- **连击奖励**: 连续成功时减少延迟，提高效率
- **失败惩罚**: 连续失败时增加延迟，避免被封
- **随机化**: 所有延迟都添加随机因子，避免规律性

### 4. 多策略语言转换
- **策略1**: URL参数强制转换 + 增强Cookie设置
- **策略2**: Cookie池轮换，尝试其他有效Cookie
- **策略3**: 重新建立英文会话
- **策略4**: 最后才使用Selenium刷新Cookie池

## 📋 使用方法

### 环境准备
```bash
pip install requests beautifulsoup4 selenium pandas
```

### 代理配置
在 `proxies.txt` 文件中配置代理（可选）：
```
socks5://用户名:密码@IP:端口
socks5://用户名:密码@IP:端口
```

### 基本使用
```python
from 筛品牌增强反检测版本 import EnhancedAmazonScraper

# 创建爬虫实例
scraper = EnhancedAmazonScraper()

# 获取单个产品信息
asin = "B08N5WRWNW"
result = scraper.get_amazon_product_info(asin)

if result["title"]:
    print(f"标题: {result['title']}")
    print(f"品牌: {result['brand']}")
    print(f"品牌类型: {result['brand_type']}")
```

## 🔧 配置参数

### Cookie池配置
```python
scraper.max_cookie_pool_size = 5        # Cookie池最大大小
scraper.cookie_refresh_interval = 3600  # Cookie刷新间隔(秒)
```

### 延迟控制配置
```python
scraper.base_delay = 1.0                # 基础延迟(秒)
scraper.max_delay = 5.0                 # 最大延迟(秒)
scraper.adaptive_delay = True           # 启用自适应延迟
```

### 会话管理配置
```python
scraper.max_requests_per_session = 100  # 每会话最大请求数
scraper.session_duration_limit = 1800   # 会话持续时间限制(秒)
```

## 📊 监控和统计

### 实时统计信息
```python
print(f"成功: {scraper.success_count}")
print(f"失败: {scraper.error_count}")
print(f"Cookie池大小: {len(scraper.cookie_pool)}")
print(f"请求计数: {scraper.request_count}")
print(f"成功连击: {scraper.success_streak}")
print(f"失败连击: {scraper.failure_streak}")
```

### Cookie池状态
```python
for i, cookie in enumerate(scraper.cookie_pool):
    success_rate = cookie['success_count'] / max(cookie['success_count'] + cookie['failure_count'], 1)
    print(f"Cookie {i+1}: 成功率 {success_rate:.2%}")
```

## 🛡️ 反检测特性

### 1. 浏览器指纹伪装
- 随机User-Agent轮换
- 真实的屏幕分辨率和颜色深度
- 合理的时区和语言设置
- 完整的浏览器特征模拟

### 2. 请求行为模拟
- 人类化的请求间隔
- 随机化的请求顺序
- 真实的错误处理逻辑
- 自然的重试机制

### 3. 会话管理
- 定期会话刷新
- Cookie有效性监控
- 代理健康检查
- 智能故障转移

## ⚠️ 注意事项

1. **合规使用**: 请遵守Amazon的使用条款和robots.txt
2. **频率控制**: 不要设置过于激进的延迟参数
3. **代理质量**: 使用高质量的代理服务，避免被检测
4. **监控日志**: 密切关注运行日志，及时调整策略
5. **数据备份**: 定期备份Cookie池和配置文件

## 🔍 故障排除

### Cookie池为空
```python
# 手动刷新Cookie池
scraper.refresh_cookie_pool()
```

### 所有代理被暂停
```python
# 清空暂停列表
scraper.suspended_proxies.clear()
```

### 会话异常
```python
# 强制刷新会话
scraper.refresh_session()
```

## 📈 性能优化建议

1. **Cookie池大小**: 建议保持3-5个有效Cookie
2. **代理数量**: 至少准备10个以上高质量代理
3. **延迟设置**: 基础延迟建议1-2秒，最大延迟5-10秒
4. **并发控制**: 单线程运行，避免过度并发
5. **监控调整**: 根据成功率实时调整参数

## 🆚 与原版本对比

| 特性 | 原版本 | 增强版本 |
|------|--------|----------|
| Cookie管理 | 每次中文页面都启动Selenium | Cookie池管理，减少90%的Selenium使用 |
| 反检测能力 | 基础User-Agent轮换 | 完整浏览器指纹伪装 |
| 延迟控制 | 固定延迟 | 智能自适应延迟 |
| 语言转换 | 单一策略 | 多策略智能转换 |
| 会话管理 | 长期会话 | 定期刷新会话 |
| 错误处理 | 简单重试 | 智能故障转移 |

通过这些改进，新版本在保持高成功率的同时，大幅提升了运行效率和稳定性。
