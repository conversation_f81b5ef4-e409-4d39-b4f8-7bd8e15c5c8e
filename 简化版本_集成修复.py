#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版本 - 集成手机验证修复的Amazon爬虫
不依赖额外库，专注于核心功能
"""

import sys
import os
import time
import logging
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simplified_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def apply_phone_verification_patch():
    """应用手机验证修复补丁"""
    logger.info("📱 应用手机验证修复补丁...")
    
    try:
        if os.path.exists('快速修复_手机验证.py'):
            # 读取修复文件内容
            with open('快速修复_手机验证.py', 'r', encoding='utf-8') as f:
                patch_content = f.read()
            
            # 安全执行补丁（跳过可能的错误）
            try:
                exec(patch_content)
                logger.info("✅ 手机验证修复补丁应用成功")
                return True
            except Exception as e:
                logger.warning(f"补丁执行警告: {e}")
                logger.info("💡 继续使用基础功能")
                return True
        else:
            logger.warning("未找到手机验证修复文件")
            return True
            
    except Exception as e:
        logger.error(f"应用补丁失败: {e}")
        return False

def create_enhanced_scraper():
    """创建增强版爬虫（简化版）"""
    logger.info("🚀 创建简化增强版爬虫...")
    
    try:
        # 导入优化版本
        import importlib.util
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)
        
        # 获取必要的类
        OptimizedAmazonScraper = optimized_module.OptimizedAmazonScraper
        ChallengeResult = optimized_module.ChallengeResult
        
        class SimplifiedEnhancedScraper(OptimizedAmazonScraper):
            """简化增强版Amazon爬虫"""
            
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                logger.info("🎯 简化增强版Amazon爬虫初始化完成")
            
            def _handle_phone_verification(self, response, soup, start_time):
                """增强的手机验证处理（使用已应用的补丁）"""
                logger.info("📱 使用增强版手机验证处理...")
                
                try:
                    # 调用父类的处理方法（已经被补丁增强）
                    result = super()._handle_phone_verification(response, soup, start_time)
                    
                    if result.solved:
                        logger.info("✅ 手机验证处理成功")
                    else:
                        logger.warning("❌ 手机验证处理失败")
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"手机验证处理异常: {e}")
                    return ChallengeResult(
                        solved=False,
                        method="异常处理",
                        time_taken=time.time() - start_time,
                        details=f"处理异常: {str(e)}"
                    )
            
            def get_status_summary(self):
                """获取状态摘要"""
                summary = super().get_status_summary()
                summary.update({
                    "version": "Simplified Enhanced v1.0",
                    "phone_verification_patch": "Applied"
                })
                return summary
        
        logger.info("✅ 简化增强版爬虫创建成功！")
        return SimplifiedEnhancedScraper
        
    except Exception as e:
        logger.error(f"创建增强版爬虫失败: {e}")
        return None

def run_gui_mode():
    """运行GUI模式"""
    logger.info("🖥️ 启动GUI模式...")
    
    try:
        # 导入GUI模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)
        
        ScraperGUI = optimized_module.ScraperGUI
        
        # 创建增强版爬虫
        EnhancedScraper = create_enhanced_scraper()
        if not EnhancedScraper:
            logger.error("无法创建增强版爬虫")
            return
        
        # 启动GUI
        gui = ScraperGUI(EnhancedScraper)
        gui.run()
        
    except Exception as e:
        logger.error(f"GUI模式启动失败: {e}")

def run_cli_mode():
    """运行命令行模式"""
    logger.info("💻 启动命令行模式...")
    
    try:
        # 创建增强版爬虫
        EnhancedScraper = create_enhanced_scraper()
        if not EnhancedScraper:
            logger.error("无法创建增强版爬虫")
            return
        
        # 创建爬虫实例
        scraper = EnhancedScraper()
        
        try:
            # 开始处理
            scraper.start_processing()
            
            # 等待用户中断
            logger.info("🚀 简化增强版爬虫正在运行...")
            logger.info("按 Ctrl+C 停止运行")
            
            while True:
                time.sleep(10)
                status = scraper.get_status_summary()
                logger.info(f"📊 当前状态: 已处理 {status.get('processed', 0)} 个产品")
                
        except KeyboardInterrupt:
            logger.info("🛑 用户中断，正在停止...")
        finally:
            scraper.cleanup()
            
    except Exception as e:
        logger.error(f"命令行模式运行失败: {e}")

def show_features():
    """显示功能特性"""
    features = """
🎯 简化增强版Amazon爬虫功能特性

📱 手机验证破解:
   ✅ 5种原有破解策略
   ✅ 跳过链接检测
   ✅ 虚假手机号提交
   ✅ 返回导航绕过
   ✅ 直接访问尝试
   ✅ Selenium高级操作

🛡️ 反检测技术:
   ✅ 动态User-Agent
   ✅ 随机请求头
   ✅ 智能代理管理
   ✅ 自动故障恢复
   ✅ 代理冷却机制

🚀 核心功能:
   ✅ 智能代理管理
   ✅ 自动故障恢复
   ✅ 实时状态监控
   ✅ 详细调试信息
   ✅ 多线程处理
   ✅ GUI和CLI双模式

📊 支持的验证类型:
   ✅ IMAGE_CAPTCHA (图像验证码)
   ✅ CLICK_CONTINUE (点击继续)
   ✅ ROBOT_CHECK (机器人检查)
   ✅ PHONE_VERIFICATION (手机验证) - 增强
   ✅ EMAIL_VERIFICATION (邮箱验证)

🔧 调试和监控:
   ✅ 详细日志记录
   ✅ 页面内容保存
   ✅ 实时状态显示
   ✅ 错误自动恢复
   ✅ 性能统计分析

💡 优势:
   ✅ 无需额外依赖库
   ✅ 快速启动和运行
   ✅ 稳定可靠
   ✅ 易于维护
"""
    print(features)

def run_test_mode():
    """运行测试模式"""
    logger.info("🧪 启动测试模式...")
    
    try:
        # 运行专门的手机验证测试
        if os.path.exists('专门测试手机验证.py'):
            logger.info("📱 运行手机验证测试...")
            exec(open('专门测试手机验证.py', encoding='utf-8').read())
        else:
            logger.warning("未找到手机验证测试文件")
            
    except Exception as e:
        logger.error(f"测试模式运行失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化增强版Amazon爬虫')
    parser.add_argument('--cli', action='store_true', help='使用命令行模式')
    parser.add_argument('--features', action='store_true', help='显示功能特性')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    
    args = parser.parse_args()
    
    print("🎯 简化增强版Amazon爬虫")
    print("=" * 50)
    
    if args.features:
        show_features()
        return
    
    if args.test:
        run_test_mode()
        return
    
    # 应用手机验证修复补丁
    if not apply_phone_verification_patch():
        logger.error("补丁应用失败，退出")
        return
    
    if args.cli:
        run_cli_mode()
    else:
        run_gui_mode()

if __name__ == "__main__":
    main()
