#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手机验证破解功能
"""

import sys
import os
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_verification_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_phone_verification_bypass():
    """测试手机验证破解功能"""
    
    print("🧪 测试手机验证破解功能")
    print("=" * 60)
    
    try:
        # 首先应用修复补丁
        from 快速修复_手机验证 import apply_patches
        if not apply_patches():
            print("❌ 补丁应用失败")
            return False
        
        # 导入优化版本
        from 优化版本_amazon_scraper import OptimizedAmazonScraper
        
        # 创建爬虫实例
        scraper = OptimizedAmazonScraper(max_workers=1)  # 单线程测试
        
        # 测试ASIN列表（这些ASIN更容易触发验证）
        test_asins = [
            'B071P43VQD',  # 之前触发过手机验证的ASIN
            'B077YYF563',
            'B07CXQ99P3',
            'B0CCNVCKPX',
            'B082BGM4H3'
        ]
        
        print(f"📋 开始测试 {len(test_asins)} 个ASIN")
        print("💡 目标: 触发手机验证并测试破解功能")
        print()
        
        success_count = 0
        phone_verification_count = 0
        bypass_success_count = 0
        
        for i, asin in enumerate(test_asins, 1):
            print(f"🔄 测试 {i}/{len(test_asins)}: {asin}")
            
            try:
                # 处理单个ASIN
                result = scraper.get_product_info(asin)
                
                if result:
                    success_count += 1
                    print(f"✅ ASIN {asin} 处理成功")
                    
                    # 检查是否包含产品信息
                    if result.get('title'):
                        print(f"   标题: {result['title'][:50]}...")
                    if result.get('price'):
                        print(f"   价格: {result['price']}")
                else:
                    print(f"❌ ASIN {asin} 处理失败")
                
                # 检查统计信息中的验证挑战
                stats = scraper.get_stats()
                current_challenges = stats.get('challenges_detected', 0)
                
                if current_challenges > phone_verification_count:
                    phone_verification_count = current_challenges
                    print(f"🛡️  检测到验证挑战，总计: {current_challenges}")
                    
                    # 检查是否有成功的破解
                    solved_challenges = stats.get('challenges_solved', 0)
                    if solved_challenges > bypass_success_count:
                        bypass_success_count = solved_challenges
                        print(f"🎉 验证挑战破解成功，总计: {solved_challenges}")
                
                # 等待一段时间避免过于频繁的请求
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"测试ASIN {asin} 时出错: {e}")
                print(f"❌ ASIN {asin} 测试出错: {e}")
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果统计:")
        print(f"  测试ASIN数: {len(test_asins)}")
        print(f"  成功处理数: {success_count}")
        print(f"  成功率: {success_count/len(test_asins)*100:.1f}%")
        print(f"  检测到的验证挑战: {phone_verification_count}")
        print(f"  成功破解的挑战: {bypass_success_count}")
        
        if phone_verification_count > 0:
            bypass_rate = (bypass_success_count / phone_verification_count) * 100
            print(f"  验证挑战破解率: {bypass_rate:.1f}%")
        
        # 显示详细统计
        final_stats = scraper.get_stats()
        print(f"\n📈 详细统计:")
        for key, value in final_stats.items():
            print(f"  {key}: {value}")
        
        # 检查是否生成了调试文件
        if os.path.exists('phone_verification_debug.html'):
            print(f"\n📁 调试文件已生成:")
            print(f"  phone_verification_debug.html - 手机验证页面内容")
            
            # 显示文件大小
            file_size = os.path.getsize('phone_verification_debug.html')
            print(f"  文件大小: {file_size} bytes")
        
        print("=" * 60)
        
        # 评估测试结果
        if phone_verification_count > 0:
            print("🎯 测试评估:")
            print("✅ 成功触发了手机验证挑战")
            
            if bypass_success_count > 0:
                print("✅ 手机验证破解功能正常工作")
                print("🎉 测试完全成功！")
                return True
            else:
                print("⚠️  手机验证破解功能需要进一步优化")
                print("💡 建议检查调试日志和HTML文件")
                return False
        else:
            print("⚠️  未能触发手机验证挑战")
            print("💡 可能需要:")
            print("   - 使用不同的代理")
            print("   - 增加请求频率")
            print("   - 尝试不同的ASIN")
            return False
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖都已正确安装")
        return False
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        print(f"❌ 测试失败: {e}")
        return False

def analyze_debug_files():
    """分析调试文件"""
    print("\n🔍 分析调试文件...")
    
    debug_files = [
        'phone_verification_debug.html',
        'phone_verification_test.log'
    ]
    
    for file_path in debug_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"📁 {file_path}: {file_size} bytes")
            
            if file_path.endswith('.html'):
                # 分析HTML文件
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    if 'phone' in content.lower():
                        print("   ✅ 包含手机验证相关内容")
                    if 'verification' in content.lower():
                        print("   ✅ 包含验证相关内容")
                    if 'captcha' in content.lower():
                        print("   ⚠️  可能包含验证码内容")
                        
                except Exception as e:
                    print(f"   ❌ 分析文件失败: {e}")
        else:
            print(f"📁 {file_path}: 不存在")

def main():
    """主函数"""
    print("🧪 手机验证破解功能测试工具")
    print("=" * 60)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--analyze':
        analyze_debug_files()
        return
    
    # 运行测试
    success = test_phone_verification_bypass()
    
    # 分析调试文件
    analyze_debug_files()
    
    if success:
        print("\n🎉 测试成功完成！")
        print("💡 手机验证破解功能已验证可用")
    else:
        print("\n⚠️  测试需要进一步优化")
        print("💡 请查看日志文件了解详细信息")
    
    print("\n使用说明:")
    print("  python 测试手机验证破解.py           # 运行完整测试")
    print("  python 测试手机验证破解.py --analyze # 仅分析调试文件")

if __name__ == "__main__":
    main()
