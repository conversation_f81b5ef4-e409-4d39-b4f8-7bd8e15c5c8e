#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断手机验证问题的脚本
"""

import sys
import os
import inspect
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def diagnose_phone_verification():
    """诊断手机验证处理问题"""
    logger.info("🔍 开始诊断手机验证问题...")
    
    try:
        # 1. 导入优化版本模块
        logger.info("📦 导入优化版本模块...")
        from 优化版本_amazon_scraper import ChallengeHandler, ChallengeType, OptimizedAmazonScraper
        
        # 2. 检查ChallengeHandler类的方法
        logger.info("🔧 检查ChallengeHandler类的方法...")
        handler_methods = [method for method in dir(ChallengeHandler) if not method.startswith('_') or method.startswith('_handle')]
        logger.info(f"ChallengeHandler方法: {handler_methods}")
        
        # 3. 检查是否有_handle_phone_verification方法
        if hasattr(ChallengeHand<PERSON>, '_handle_phone_verification'):
            logger.info("✅ _handle_phone_verification方法存在")
            
            # 获取方法源码
            try:
                source = inspect.getsource(ChallengeHandler._handle_phone_verification)
                logger.info(f"📝 方法源码长度: {len(source)} 字符")
                logger.info(f"📝 方法前几行:\n{source[:200]}...")
            except Exception as e:
                logger.error(f"无法获取方法源码: {e}")
        else:
            logger.error("❌ _handle_phone_verification方法不存在")
        
        # 4. 检查是否有相关的辅助方法
        phone_methods = [method for method in dir(ChallengeHandler) if 'phone' in method.lower()]
        logger.info(f"包含'phone'的方法: {phone_methods}")
        
        # 5. 创建一个测试实例
        logger.info("🧪 创建测试实例...")
        import requests
        from 优化版本_amazon_scraper import ProxyManager
        
        session = requests.Session()
        proxy_manager = ProxyManager()
        handler = ChallengeHandler(session, proxy_manager)
        
        # 6. 测试方法调用
        logger.info("🎯 测试方法调用...")
        try:
            # 创建模拟的响应和soup对象
            import requests
            from bs4 import BeautifulSoup
            
            # 创建一个简单的测试响应
            test_response = requests.Response()
            test_response.status_code = 200
            test_response.url = "https://www.amazon.com/ap/signin"
            test_response._content = b'<html><body><div>Phone verification test</div></body></html>'
            
            test_soup = BeautifulSoup(test_response.content, 'html.parser')
            
            # 尝试调用handle_challenge方法
            result = handler.handle_challenge(ChallengeType.PHONE_VERIFICATION, test_response, test_soup)
            logger.info(f"📊 测试结果: solved={result.solved}, method={result.method_used}, error={result.error_message}")
            
        except Exception as e:
            logger.error(f"❌ 测试调用失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        # 7. 检查快速修复补丁是否已应用
        logger.info("🔧 检查快速修复补丁状态...")
        if os.path.exists('快速修复_手机验证.py'):
            logger.info("✅ 快速修复文件存在")
            
            # 尝试应用补丁
            try:
                exec(open('快速修复_手机验证.py', encoding='utf-8').read())
                logger.info("✅ 快速修复补丁应用成功")
                
                # 重新测试
                logger.info("🔄 重新测试补丁后的效果...")
                result2 = handler.handle_challenge(ChallengeType.PHONE_VERIFICATION, test_response, test_soup)
                logger.info(f"📊 补丁后结果: solved={result2.solved}, method={result2.method_used}, error={result2.error_message}")
                
            except Exception as e:
                logger.error(f"❌ 应用补丁失败: {e}")
        else:
            logger.error("❌ 快速修复文件不存在")
        
        # 8. 检查调试方法
        debug_methods = [method for method in dir(ChallengeHandler) if 'debug' in method.lower()]
        logger.info(f"调试相关方法: {debug_methods}")
        
        # 9. 检查破解方法
        bypass_methods = [method for method in dir(ChallengeHandler) if 'bypass' in method.lower() or 'attempt' in method.lower()]
        logger.info(f"破解相关方法: {bypass_methods}")
        
        logger.info("🎉 诊断完成")
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def check_current_implementation():
    """检查当前实现的详细情况"""
    logger.info("🔍 检查当前实现...")
    
    try:
        from 优化版本_amazon_scraper import ChallengeHandler
        
        # 检查_handle_phone_verification方法的实现
        if hasattr(ChallengeHandler, '_handle_phone_verification'):
            method = getattr(ChallengeHandler, '_handle_phone_verification')
            logger.info(f"方法对象: {method}")
            logger.info(f"方法类型: {type(method)}")
            
            # 尝试获取方法的详细信息
            try:
                sig = inspect.signature(method)
                logger.info(f"方法签名: {sig}")
            except Exception as e:
                logger.error(f"无法获取方法签名: {e}")
        
        # 检查是否有相关的辅助方法
        for attr_name in dir(ChallengeHandler):
            if 'phone' in attr_name.lower() or 'verification' in attr_name.lower():
                attr = getattr(ChallengeHandler, attr_name)
                if callable(attr):
                    logger.info(f"找到相关方法: {attr_name} - {type(attr)}")
                    
    except Exception as e:
        logger.error(f"检查实现时出错: {e}")

def test_direct_method_call():
    """直接测试方法调用"""
    logger.info("🧪 直接测试方法调用...")
    
    try:
        from 优化版本_amazon_scraper import ChallengeHandler, ProxyManager
        import requests
        from bs4 import BeautifulSoup
        
        # 创建实例
        session = requests.Session()
        proxy_manager = ProxyManager()
        handler = ChallengeHandler(session, proxy_manager)
        
        # 创建测试数据
        test_response = requests.Response()
        test_response.status_code = 200
        test_response.url = "https://www.amazon.com/ap/signin"
        test_response._content = b'''
        <html>
        <body>
            <div>Phone verification required</div>
            <form action="/ap/signin" method="post">
                <input type="hidden" name="appActionToken" value="test123">
                <input type="tel" name="phoneNumber" placeholder="Enter phone number">
                <button type="submit">Continue</button>
                <a href="/skip">Skip for now</a>
            </form>
        </body>
        </html>
        '''
        
        test_soup = BeautifulSoup(test_response.content, 'html.parser')
        
        # 直接调用_handle_phone_verification方法
        if hasattr(handler, '_handle_phone_verification'):
            logger.info("📞 直接调用_handle_phone_verification方法...")
            try:
                import time
                result = handler._handle_phone_verification(test_response, test_soup, time.time())
                logger.info(f"✅ 直接调用成功: {result}")
            except Exception as e:
                logger.error(f"❌ 直接调用失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
        else:
            logger.error("❌ _handle_phone_verification方法不存在")
            
    except Exception as e:
        logger.error(f"测试直接调用时出错: {e}")

def main():
    """主函数"""
    print("🔍 手机验证问题诊断工具")
    print("=" * 50)
    
    diagnose_phone_verification()
    print("\n" + "=" * 50)
    
    check_current_implementation()
    print("\n" + "=" * 50)
    
    test_direct_method_call()
    print("\n" + "=" * 50)
    
    print("🎯 诊断完成！请查看上面的输出来了解问题所在。")

if __name__ == "__main__":
    main()
