#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速品牌筛选版本 - 优化速度，移除调试文件保存
基于纯requests架构，具备完整的品牌匹配逻辑
"""

import sys
import logging
import pandas as pd
from 纯requests版本_amazon_scraper import PureRequestsAmazonScraper
import time

# 配置日志 - 减少日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('快速品牌筛选.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def load_asins_from_file(filename=None):
    """从文件加载ASIN列表，支持txt和xlsx格式"""
    # 自动检测文件
    possible_files = ['asin.xlsx', 'asins.txt', 'asins.xlsx', 'asin.txt']
    
    if filename:
        possible_files.insert(0, filename)
    
    for file_path in possible_files:
        try:
            if file_path.endswith('.xlsx'):
                # 读取Excel文件
                df = pd.read_excel(file_path)
                # 尝试不同的列名
                asin_column = None
                for col in df.columns:
                    if 'asin' in col.lower() or 'ASIN' in col:
                        asin_column = col
                        break
                
                if asin_column:
                    asins = df[asin_column].dropna().astype(str).tolist()
                else:
                    # 如果没找到ASIN列，使用第一列
                    asins = df.iloc[:, 0].dropna().astype(str).tolist()
                
                # 过滤有效的ASIN
                asins = [asin.strip() for asin in asins if asin.strip() and len(asin.strip()) == 10]
                logger.info(f"📋 从 {file_path} 加载了 {len(asins)} 个ASIN")
                return asins
                
            else:
                # 读取文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    asins = [line.strip() for line in f if line.strip()]
                logger.info(f"📋 从 {file_path} 加载了 {len(asins)} 个ASIN")
                return asins
                
        except FileNotFoundError:
            continue
        except Exception as e:
            logger.warning(f"⚠️ 读取文件 {file_path} 时出错: {e}")
            continue
    
    logger.error(f"❌ 未找到ASIN文件，尝试了: {', '.join(possible_files)}")
    return []

def save_results_to_excel(results, filename='快速品牌筛选结果.xlsx'):
    """保存结果到Excel文件"""
    try:
        if not results:
            logger.warning("⚠️ 没有结果可保存")
            return
        
        # 分类结果
        unique_brands = []
        non_unique_brands = []
        errors = []
        
        for result in results:
            if result.get('error'):
                errors.append(result)
            elif result.get('是否唯一品牌'):
                unique_brands.append(result)
            else:
                non_unique_brands.append(result)
        
        # 创建Excel文件
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 唯一品牌工作表
            if unique_brands:
                df_unique = pd.DataFrame(unique_brands)
                df_unique.to_excel(writer, sheet_name='唯一品牌', index=False)
                logger.info(f"✅ 保存了 {len(unique_brands)} 个唯一品牌产品")
            
            # 非唯一品牌工作表
            if non_unique_brands:
                df_non_unique = pd.DataFrame(non_unique_brands)
                df_non_unique.to_excel(writer, sheet_name='非唯一品牌', index=False)
                logger.info(f"📊 保存了 {len(non_unique_brands)} 个非唯一品牌产品")
            
            # 错误工作表
            if errors:
                df_errors = pd.DataFrame(errors)
                df_errors.to_excel(writer, sheet_name='处理错误', index=False)
                logger.info(f"❌ 保存了 {len(errors)} 个处理错误")
        
        logger.info(f"🎉 结果已保存到 {filename}")
        
        # 打印统计信息
        logger.info("📊 最终统计:")
        logger.info(f"   ✅ 唯一品牌: {len(unique_brands)}")
        logger.info(f"   ❌ 非唯一品牌: {len(non_unique_brands)}")
        logger.info(f"   🚫 处理错误: {len(errors)}")
        logger.info(f"   📋 总计: {len(results)}")
        
    except Exception as e:
        logger.error(f"❌ 保存结果时出错: {e}")

def process_asins_fast(asins):
    """快速处理ASIN列表"""
    logger.info(f"🚀 快速模式：开始处理 {len(asins)} 个ASIN")
    
    scraper = PureRequestsAmazonScraper()
    results = []
    
    # 统计计数器
    unique_count = 0
    non_unique_count = 0
    error_count = 0
    start_time = time.time()
    
    for i, asin in enumerate(asins, 1):
        try:
            product_info = scraper.get_product_info(asin)
            
            if product_info:
                results.append(product_info)
                
                # 实时统计
                is_unique = product_info.get('是否唯一品牌', False)
                brand = product_info.get('品牌', 'N/A')
                
                if is_unique:
                    unique_count += 1
                    status = "✅"
                else:
                    non_unique_count += 1
                    status = "❌"
                
                # 计算速度
                elapsed = time.time() - start_time
                speed = i / elapsed * 60  # 每分钟处理数量
                
                logger.info(f"{status} {i}/{len(asins)} {asin} '{brand}' | 唯一:{unique_count} 非唯一:{non_unique_count} 错误:{error_count} | 速度:{speed:.1f}/分钟")
                
            else:
                error_count += 1
                error_result = {
                    'ASIN': asin,
                    'error': True,
                    'error_message': '获取产品信息失败'
                }
                results.append(error_result)
                logger.error(f"🚫 {i}/{len(asins)} {asin} 失败")
                
        except Exception as e:
            error_count += 1
            error_result = {
                'ASIN': asin,
                'error': True,
                'error_message': str(e)
            }
            results.append(error_result)
            logger.error(f"🚫 {i}/{len(asins)} {asin} 异常: {e}")
        
        # 每100个产品显示详细进度
        if i % 100 == 0:
            elapsed = time.time() - start_time
            speed = i / elapsed * 60
            remaining = (len(asins) - i) / speed * 60 if speed > 0 else 0
            logger.info(f"📊 进度: {i}/{len(asins)} ({i/len(asins)*100:.1f}%) | 速度: {speed:.1f}/分钟 | 预计剩余: {remaining:.0f}分钟")
    
    # 最终统计
    total_time = time.time() - start_time
    avg_speed = len(asins) / total_time * 60
    logger.info(f"🏁 处理完成！总用时: {total_time/60:.1f}分钟 | 平均速度: {avg_speed:.1f}/分钟")
    
    return results

def main():
    """主函数"""
    logger.info("🚀 快速品牌筛选系统启动")
    logger.info("=" * 60)
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 测试模式
        logger.info("🧪 快速测试模式")
        test_asins = ['B0BSHF7LLL', 'B097XQ49D4']
        results = process_asins_fast(test_asins)
        save_results_to_excel(results, '快速测试结果.xlsx')
    else:
        # 正常模式
        logger.info("🚀 快速处理模式")
        asins = load_asins_from_file()
        
        if not asins:
            logger.error("❌ 没有找到要处理的ASIN")
            return
        
        results = process_asins_fast(asins)
        save_results_to_excel(results)
    
    logger.info("🎉 快速品牌筛选处理完成！")

if __name__ == "__main__":
    main()
