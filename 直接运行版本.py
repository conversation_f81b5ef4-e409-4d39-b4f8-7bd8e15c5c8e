#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接运行版本 - 集成手机验证修复的Amazon爬虫
可以直接运行，无需额外配置
"""

import sys
import os
import time
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('direct_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def apply_patches_and_run():
    """应用补丁并运行爬虫"""
    logger.info("🚀 直接运行版本启动...")
    
    try:
        # 1. 首先应用手机验证修复补丁
        logger.info("📱 应用手机验证修复...")
        if os.path.exists('快速修复_手机验证.py'):
            try:
                # 安全执行补丁
                with open('快速修复_手机验证.py', 'r', encoding='utf-8') as f:
                    patch_code = f.read()
                
                # 创建安全的执行环境
                patch_globals = {}
                exec(patch_code, patch_globals)
                
                # 调用补丁应用函数
                if 'apply_patches' in patch_globals:
                    patch_result = patch_globals['apply_patches']()
                    if patch_result:
                        logger.info("✅ 手机验证修复应用成功")
                    else:
                        logger.warning("⚠️ 手机验证修复应用有警告，但继续运行")
                else:
                    logger.warning("未找到apply_patches函数，跳过补丁应用")
                    
            except Exception as e:
                logger.warning(f"补丁应用警告: {e}")
                logger.info("💡 继续使用基础功能运行")
        
        # 2. 导入并运行优化版本
        logger.info("🔧 导入优化版本爬虫...")
        import importlib.util
        
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)
        
        # 获取爬虫类
        OptimizedAmazonScraper = optimized_module.OptimizedAmazonScraper
        
        # 创建爬虫实例
        logger.info("🎯 创建爬虫实例...")
        scraper = OptimizedAmazonScraper()
        
        # 显示状态
        logger.info("📊 爬虫状态:")
        status = scraper.get_status_summary()
        for key, value in status.items():
            logger.info(f"   {key}: {value}")
        
        # 开始处理
        logger.info("🚀 开始处理ASIN...")
        scraper.start_processing()
        
        # 运行监控循环
        logger.info("📈 进入监控模式...")
        logger.info("按 Ctrl+C 停止运行")
        
        try:
            while True:
                time.sleep(30)  # 每30秒显示一次状态
                status = scraper.get_status_summary()
                processed = status.get('processed', 0)
                total = status.get('total_asins', 0)
                success_rate = status.get('success_rate', 0)
                
                logger.info(f"📊 处理进度: {processed}/{total} (成功率: {success_rate:.1f}%)")
                
                # 检查是否完成
                if processed >= total and total > 0:
                    logger.info("🎉 所有ASIN处理完成！")
                    break
                    
        except KeyboardInterrupt:
            logger.info("🛑 用户中断，正在停止...")
        
        # 清理资源
        logger.info("🧹 清理资源...")
        scraper.cleanup()
        logger.info("✅ 运行完成")
        
    except Exception as e:
        logger.error(f"运行失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def run_gui_mode():
    """运行GUI模式"""
    logger.info("🖥️ 启动GUI模式...")
    
    try:
        # 应用补丁
        if os.path.exists('快速修复_手机验证.py'):
            exec(open('快速修复_手机验证.py', encoding='utf-8').read())
            logger.info("✅ 补丁应用成功")
        
        # 导入GUI
        import importlib.util
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)
        
        ScraperGUI = optimized_module.ScraperGUI
        OptimizedAmazonScraper = optimized_module.OptimizedAmazonScraper
        
        # 启动GUI
        gui = ScraperGUI(OptimizedAmazonScraper)
        gui.run()
        
    except Exception as e:
        logger.error(f"GUI模式启动失败: {e}")

def run_test_phone_verification():
    """运行手机验证测试"""
    logger.info("🧪 运行手机验证测试...")
    
    try:
        if os.path.exists('专门测试手机验证.py'):
            logger.info("📱 执行手机验证测试脚本...")
            exec(open('专门测试手机验证.py', encoding='utf-8').read())
        else:
            logger.error("未找到手机验证测试文件")
            
    except Exception as e:
        logger.error(f"测试运行失败: {e}")

def show_status():
    """显示当前状态"""
    logger.info("📊 检查系统状态...")
    
    # 检查必要文件
    required_files = [
        '优化版本_amazon_scraper.py',
        '快速修复_手机验证.py',
        'proxies.txt',
        'asin.xlsx'
    ]
    
    logger.info("📁 文件检查:")
    for file in required_files:
        exists = os.path.exists(file)
        status = "✅" if exists else "❌"
        logger.info(f"   {status} {file}")
    
    # 检查日志文件
    log_files = ['direct_scraper.log', 'phone_verification_debug.html']
    logger.info("📄 日志文件:")
    for file in log_files:
        exists = os.path.exists(file)
        status = "✅" if exists else "❌"
        size = os.path.getsize(file) if exists else 0
        logger.info(f"   {status} {file} ({size} bytes)")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='直接运行版Amazon爬虫')
    parser.add_argument('--gui', action='store_true', help='使用GUI模式')
    parser.add_argument('--test', action='store_true', help='运行手机验证测试')
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    
    args = parser.parse_args()
    
    print("🎯 直接运行版Amazon爬虫")
    print("=" * 50)
    print("✅ 集成手机验证修复")
    print("✅ 无需额外配置")
    print("✅ 开箱即用")
    print("=" * 50)
    
    if args.status:
        show_status()
    elif args.test:
        run_test_phone_verification()
    elif args.gui:
        run_gui_mode()
    else:
        # 默认运行CLI模式
        apply_patches_and_run()

if __name__ == "__main__":
    main()
