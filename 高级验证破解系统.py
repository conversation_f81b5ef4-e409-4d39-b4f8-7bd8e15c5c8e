#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Amazon验证破解系统
基于HTML结构分析的多策略验证绕过
"""

import requests
import time
import random
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs
import logging

logger = logging.getLogger(__name__)

class AdvancedVerificationBypass:
    """高级验证绕过系统"""
    
    def __init__(self, session):
        self.session = session
        self.bypass_stats = {
            'form_submit': 0,
            'direct_redirect': 0,
            'parameter_manipulation': 0,
            'session_bypass': 0
        }
    
    def analyze_verification_page(self, response, soup):
        """深度分析验证页面结构"""
        analysis = {
            'page_type': 'unknown',
            'forms': [],
            'scripts': [],
            'redirects': [],
            'parameters': {},
            'bypass_opportunities': []
        }
        
        # 检测页面类型
        if "Click the button below to continue shopping" in response.text:
            analysis['page_type'] = 'click_continue'
        elif "Type the characters you see" in response.text:
            analysis['page_type'] = 'captcha'
        elif "Enter your phone number" in response.text:
            analysis['page_type'] = 'phone_verification'
        
        # 分析表单
        forms = soup.find_all('form')
        for form in forms:
            form_data = {
                'action': form.get('action', ''),
                'method': form.get('method', 'get').lower(),
                'inputs': {}
            }
            
            # 提取所有输入字段
            inputs = form.find_all('input')
            for inp in inputs:
                name = inp.get('name', '')
                value = inp.get('value', '')
                input_type = inp.get('type', 'text')
                
                if name:
                    form_data['inputs'][name] = {
                        'value': value,
                        'type': input_type
                    }
            
            analysis['forms'].append(form_data)
        
        # 分析脚本中的重定向
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                script_content = script.string
                if 'location' in script_content.lower() or 'redirect' in script_content.lower():
                    analysis['scripts'].append(script_content)
        
        # 分析链接
        links = soup.find_all('a', href=True)
        for link in links:
            href = link.get('href')
            if href and ('continue' in href.lower() or 'skip' in href.lower()):
                analysis['redirects'].append(href)
        
        # 识别绕过机会
        if analysis['forms']:
            analysis['bypass_opportunities'].append('form_submission')
        
        for form in analysis['forms']:
            if 'amzn-r' in form['inputs']:
                analysis['bypass_opportunities'].append('direct_redirect')
                analysis['parameters']['redirect_url'] = form['inputs']['amzn-r']['value']
        
        return analysis
    
    def bypass_click_continue(self, response, soup, analysis):
        """绕过点击继续验证"""
        logger.info("🔧 尝试绕过点击继续验证...")
        
        # 策略1: 表单提交绕过
        if analysis['forms']:
            form = analysis['forms'][0]  # 使用第一个表单
            
            try:
                # 构造表单数据
                form_data = {}
                for name, field in form['inputs'].items():
                    form_data[name] = field['value']
                
                # 构造完整的action URL
                action_url = form['action']
                if not action_url.startswith('http'):
                    action_url = urljoin(response.url, action_url)
                
                logger.info(f"🔧 提交表单到: {action_url}")
                logger.info(f"🔧 表单数据: {form_data}")
                
                # 提交表单
                if form['method'] == 'post':
                    bypass_response = self.session.post(action_url, data=form_data, timeout=30)
                else:
                    bypass_response = self.session.get(action_url, params=form_data, timeout=30)
                
                logger.info(f"🔧 绕过响应状态码: {bypass_response.status_code}")

                # 检查是否成功绕过
                if bypass_response.status_code == 200 and self._is_product_page(bypass_response.text):
                    logger.info("✅ 表单提交绕过成功！")
                    self.bypass_stats['form_submit'] += 1
                    return bypass_response
                
                # 如果重定向，跟随重定向
                if bypass_response.status_code in [301, 302, 303, 307, 308]:
                    redirect_url = bypass_response.headers.get('Location')
                    if redirect_url:
                        logger.info(f"🔧 跟随重定向到: {redirect_url}")
                        final_response = self.session.get(redirect_url, timeout=30)
                        
                        if self._is_product_page(final_response.text):
                            logger.info("✅ 重定向绕过成功！")
                            self.bypass_stats['form_submit'] += 1
                            return final_response
                
            except Exception as e:
                logger.error(f"表单提交绕过失败: {e}")
        
        # 策略2: 直接重定向绕过
        if 'redirect_url' in analysis['parameters']:
            try:
                redirect_url = analysis['parameters']['redirect_url']
                if not redirect_url.startswith('http'):
                    redirect_url = urljoin(response.url, redirect_url)
                
                logger.info(f"🔧 尝试直接访问重定向URL: {redirect_url}")
                
                direct_response = self.session.get(redirect_url, timeout=30)

                if direct_response.status_code == 200 and self._is_product_page(direct_response.text):
                    logger.info("✅ 直接重定向绕过成功！")
                    self.bypass_stats['direct_redirect'] += 1
                    return direct_response
                
            except Exception as e:
                logger.error(f"直接重定向绕过失败: {e}")
        
        # 策略3: 参数操作绕过
        try:
            # 尝试修改URL参数
            parsed_url = urlparse(response.url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # 尝试访问原始产品页面
            if '/dp/' in response.url:
                asin = response.url.split('/dp/')[-1].split('?')[0].split('/')[0]
                product_url = f"{base_url}/dp/{asin}"
                
                logger.info(f"🔧 尝试直接访问产品页面: {product_url}")
                
                # 添加一些随机参数来绕过检测
                params = {
                    'ref': f'sr_1_{random.randint(1, 50)}',
                    'keywords': ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=6))
                }
                
                param_response = self.session.get(product_url, params=params, timeout=30)

                if param_response.status_code == 200 and self._is_product_page(param_response.text):
                    logger.info("✅ 参数操作绕过成功！")
                    self.bypass_stats['parameter_manipulation'] += 1
                    return param_response
                
        except Exception as e:
            logger.error(f"参数操作绕过失败: {e}")
        
        logger.warning("❌ 所有绕过策略都失败了")
        return None
    
    def _is_product_page(self, html_content):
        """检查是否是产品页面"""
        product_indicators = [
            "productTitle",
            "product-title",
            "dp-container",
            "feature-bullets",
            "bylineInfo"
        ]
        
        for indicator in product_indicators:
            if indicator in html_content:
                return True
        
        return False
    
    def get_bypass_stats(self):
        """获取绕过统计"""
        return self.bypass_stats.copy()
