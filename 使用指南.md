# 优化版亚马逊爬虫使用指南

## 快速开始

### 1. 环境准备

#### 安装依赖
```bash
pip install requests beautifulsoup4 amazoncaptcha selenium pandas openpyxl tqdm
```

#### 下载ChromeDriver
- 访问 https://chromedriver.chromium.org/
- 下载与你的Chrome版本匹配的ChromeDriver
- 将chromedriver.exe放在系统PATH中或项目目录下

### 2. 文件准备

确保以下文件存在：
- `asin.xlsx` - 包含要抓取的ASIN列表
- `proxies.txt` - 代理配置文件（格式见下文）

### 3. 运行方式

#### 方式一：GUI界面（推荐）
```bash
python 优化版本_amazon_scraper.py
```

#### 方式二：命令行模式
```bash
python 优化版本_amazon_scraper.py --cli
```

#### 方式三：测试模式
```bash
python 测试优化版本.py
```

## 配置文件格式

### proxies.txt 格式
```
协议: socks5
地址: **************
端口: 48361
用户名: username1
密码: password1

协议: socks5
地址: **************
端口: 43562
用户名: username2
密码: password2
```

### asin.xlsx 格式
Excel文件，第一列包含ASIN列表：
```
ASIN
B000HJUXMO
B07G3KS4JK
B097XQ49D4
...
```

## 功能特性

### 1. 智能反爬虫处理
- **自动检测验证挑战**：图片验证码、点击继续、机器人检查等
- **多种处理策略**：requests + Selenium双重保障
- **智能重试机制**：失败后自动重试和降级处理

### 2. 代理管理
- **智能代理选择**：基于成功率和响应时间
- **动态冷却机制**：失败代理自动进入冷却期
- **健康状态监控**：实时监控代理可用性

### 3. 数据提取
- **产品基本信息**：标题、品牌、ASIN
- **库存状态检查**：只处理断货产品
- **品牌唯一性验证**：确保品牌的独特性
- **评论数量过滤**：20-2000评论数范围
- **销售排名信息**：多类目排名数据

### 4. 用户界面
- **进度显示**：实时显示处理进度
- **日志输出**：详细的操作日志
- **统计信息**：成功率、验证码处理等统计
- **结果保存**：自动保存为Excel文件

## 使用技巧

### 1. 代理配置建议
- 使用高质量的住宅代理
- 配置至少20个以上的代理
- 定期更新代理列表
- 避免使用免费代理

### 2. 并发设置
- 建议并发线程数：10-30
- 代理数量充足时可适当增加
- 监控成功率，及时调整

### 3. 监控指标
- **请求成功率**：应保持在70%以上
- **验证挑战解决率**：应保持在50%以上
- **代理切换频率**：过高说明代理质量不佳

### 4. 异常处理
- **机器人检查**：更换IP或降低请求频率
- **验证码过多**：检查代理质量和请求头
- **连接超时**：检查网络和代理状态

## 常见问题

### Q1: 验证码识别失败怎么办？
A: 
1. 检查amazoncaptcha库是否正确安装
2. 确保验证码图片下载成功
3. 可以手动保存验证码图片进行调试

### Q2: 代理连接失败？
A:
1. 检查代理格式是否正确
2. 验证代理的用户名密码
3. 测试代理是否能正常访问网络

### Q3: Chrome浏览器启动失败？
A:
1. 确保ChromeDriver版本与Chrome匹配
2. 检查ChromeDriver是否在PATH中
3. 尝试更新Chrome浏览器

### Q4: 成功率很低怎么办？
A:
1. 检查代理质量
2. 降低并发线程数
3. 增加请求间隔时间
4. 更新User-Agent列表

## 高级配置

### 1. 自定义请求头
修改 `AntiDetectionManager` 类中的 `user_agents` 列表：
```python
self.user_agents = [
    'your-custom-user-agent-1',
    'your-custom-user-agent-2',
    # ...
]
```

### 2. 调整延迟策略
修改 `get_random_delay` 方法的参数：
```python
def get_random_delay(self, min_delay: float = 2.0, max_delay: float = 5.0):
    # 增加延迟时间
```

### 3. 自定义验证码处理
继承 `ChallengeHandler` 类并重写相关方法：
```python
class CustomChallengeHandler(ChallengeHandler):
    def _handle_image_captcha(self, response, soup, start_time):
        # 自定义验证码处理逻辑
        pass
```

## 性能优化

### 1. 内存优化
- 定期清理Selenium浏览器实例
- 限制同时运行的浏览器数量
- 及时释放大对象的引用

### 2. 网络优化
- 使用连接池复用连接
- 设置合适的超时时间
- 启用gzip压缩

### 3. 存储优化
- 分批保存结果避免内存溢出
- 使用数据库存储大量数据
- 定期清理临时文件

## 监控和维护

### 1. 日志监控
- 定期检查错误日志
- 监控成功率变化趋势
- 分析失败原因分布

### 2. 性能监控
- 监控内存和CPU使用率
- 跟踪请求响应时间
- 统计代理使用效率

### 3. 定期维护
- 更新代理列表
- 升级依赖库版本
- 调整反爬虫策略

## 法律声明

使用本工具时请遵守以下原则：
1. 遵守Amazon的服务条款
2. 不要进行恶意或过度的请求
3. 尊重网站的robots.txt文件
4. 仅用于合法的商业研究目的
5. 承担使用风险和法律责任

## 技术支持

如遇到问题，请：
1. 查看日志文件了解详细错误信息
2. 检查网络连接和代理状态
3. 确认配置文件格式正确
4. 尝试降低并发数或增加延迟

---

**注意**：本工具仅供学习和研究使用，使用者需自行承担使用风险和法律责任。
