#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版Amazon爬虫的功能
"""

from 筛品牌增强反检测版本 import EnhancedAmazonScraper
import time

def test_enhanced_scraper():
    """测试增强版爬虫功能"""
    print("🧪 开始测试增强版Amazon爬虫")
    print("=" * 50)
    
    # 创建爬虫实例
    scraper = EnhancedAmazonScraper()
    
    # 测试用的有效ASIN列表
    test_asins = [
        "B0D1XD1ZV3",  # Echo Dot (5th Gen)
        "B08C1W5N87",  # Echo Show 5
        "B07XJ8C8F5",  # Echo Dot (4th Gen)
        "B0BCQJBQTX",  # Fire TV Stick 4K Max
        "B08XVYZ1Y5"   # Echo Show 8
    ]
    
    print(f"📋 准备测试 {len(test_asins)} 个ASIN")
    print(f"🍪 当前Cookie池大小: {len(scraper.cookie_pool)}")
    print()
    
    results = []
    start_time = time.time()
    
    for i, asin in enumerate(test_asins, 1):
        print(f"🔍 [{i}/{len(test_asins)}] 测试ASIN: {asin}")
        
        # 获取产品信息
        result = scraper.get_amazon_product_info(asin)
        results.append(result)
        
        # 显示结果
        if result["title"]:
            print(f"✅ 成功获取产品信息:")
            print(f"   📝 标题: {result['title'][:60]}...")
            print(f"   🏷️  品牌: {result['brand']}")
            print(f"   📊 品牌类型: {result['brand_type']}")
        else:
            print(f"❌ 未能获取产品信息")
        
        # 显示当前统计
        print(f"   📈 当前统计 - 成功: {scraper.success_count}, 失败: {scraper.error_count}")
        print(f"   🍪 Cookie池状态: {len(scraper.cookie_pool)} 个Cookie")
        print(f"   🔄 请求计数: {scraper.request_count}")
        print(f"   ⚡ 成功连击: {scraper.success_streak}, 失败连击: {scraper.failure_streak}")
        print()
        
        # 短暂延迟
        if i < len(test_asins):
            time.sleep(2)
    
    # 测试完成，显示总结
    end_time = time.time()
    total_time = end_time - start_time
    
    print("🎯 测试完成 - 总结报告")
    print("=" * 50)
    print(f"⏱️  总耗时: {total_time:.2f} 秒")
    print(f"📊 总体统计:")
    print(f"   ✅ 成功: {scraper.success_count}")
    print(f"   ❌ 失败: {scraper.error_count}")
    print(f"   📈 成功率: {scraper.success_count / len(test_asins) * 100:.1f}%")
    print(f"   🍪 Cookie池大小: {len(scraper.cookie_pool)}")
    print(f"   🔄 总请求数: {scraper.request_count}")
    print(f"   ⚡ 平均每个ASIN请求数: {scraper.request_count / len(test_asins):.1f}")
    
    # Cookie池详细状态
    print(f"\n🍪 Cookie池详细状态:")
    for i, cookie in enumerate(scraper.cookie_pool, 1):
        total_requests = cookie['success_count'] + cookie['failure_count']
        success_rate = cookie['success_count'] / max(total_requests, 1) * 100
        print(f"   Cookie {i}: 成功率 {success_rate:.1f}% ({cookie['success_count']}/{total_requests})")
    
    # 成功获取的产品信息
    successful_results = [r for r in results if r["title"]]
    print(f"\n📋 成功获取的产品信息 ({len(successful_results)} 个):")
    for result in successful_results:
        print(f"   🔸 {result['asin']}: {result['title'][:40]}... | 品牌: {result['brand']}")
    
    # 性能分析
    if scraper.success_count > 0:
        avg_time_per_success = total_time / scraper.success_count
        print(f"\n⚡ 性能分析:")
        print(f"   平均每个成功请求耗时: {avg_time_per_success:.2f} 秒")
        print(f"   预估每小时可处理: {3600 / avg_time_per_success:.0f} 个ASIN")
    
    return results

def test_cookie_pool_management():
    """测试Cookie池管理功能"""
    print("\n🧪 测试Cookie池管理功能")
    print("=" * 30)
    
    scraper = EnhancedAmazonScraper()
    
    print(f"初始Cookie池大小: {len(scraper.cookie_pool)}")
    
    # 测试获取最佳Cookie
    best_cookie = scraper.get_best_cookie()
    if best_cookie:
        print(f"✅ 成功获取最佳Cookie")
        print(f"   成功次数: {best_cookie['success_count']}")
        print(f"   失败次数: {best_cookie['failure_count']}")
    
    # 测试Cookie统计更新
    print("\n测试Cookie统计更新...")
    scraper.update_cookie_stats(best_cookie, success=True)
    scraper.update_cookie_stats(best_cookie, success=True)
    scraper.update_cookie_stats(best_cookie, success=False)
    
    print(f"更新后 - 成功: {best_cookie['success_count']}, 失败: {best_cookie['failure_count']}")

def test_fingerprint_rotation():
    """测试浏览器指纹轮换"""
    print("\n🧪 测试浏览器指纹轮换")
    print("=" * 30)
    
    scraper = EnhancedAmazonScraper()
    
    print(f"指纹池大小: {len(scraper.fingerprint_pool)}")
    
    # 测试获取多个指纹
    for i in range(3):
        fingerprint = scraper.get_next_fingerprint()
        print(f"指纹 {i+1}: {fingerprint['user_agent'][:50]}...")

if __name__ == "__main__":
    try:
        # 主要功能测试
        results = test_enhanced_scraper()
        
        # Cookie池管理测试
        test_cookie_pool_management()
        
        # 指纹轮换测试
        test_fingerprint_rotation()
        
        print("\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
