#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控亚马逊爬虫运行状态
"""

import time
import os
import sys
from datetime import datetime, timedelta
import threading

def monitor_log_file(log_file="scraper.log"):
    """监控日志文件"""
    if not os.path.exists(log_file):
        print(f"⚠️  日志文件 {log_file} 不存在")
        return
    
    print(f"📊 监控日志文件: {log_file}")
    print("按 Ctrl+C 停止监控")
    print("-" * 60)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            # 移到文件末尾
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    print(line.strip())
                else:
                    time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"监控出错: {e}")

def analyze_current_status():
    """分析当前运行状态"""
    print("🔍 分析当前运行状态...")
    print("=" * 60)
    
    # 统计信息
    stats = {
        'total_requests': 0,
        'successful_requests': 0,
        'failed_requests': 0,
        'challenges_detected': 0,
        'phone_verifications': 0,
        'captcha_challenges': 0,
        'robot_checks': 0,
        'proxy_switches': 0,
        'processed_asins': 0
    }
    
    # 分析最近的日志（如果存在）
    log_files = ['scraper.log', 'amazon_scraper.log']
    recent_logs = []
    
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 获取最近100行
                    recent_logs.extend(lines[-100:])
                    break
            except Exception as e:
                print(f"读取日志文件 {log_file} 失败: {e}")
    
    if not recent_logs:
        print("📝 没有找到日志文件，尝试从控制台输出分析...")
        return
    
    # 分析日志内容
    for line in recent_logs:
        line_lower = line.lower()
        
        if '请求成功' in line:
            stats['successful_requests'] += 1
        elif '请求失败' in line or '处理失败' in line:
            stats['failed_requests'] += 1
        elif 'phone_verification' in line_lower:
            stats['phone_verifications'] += 1
            stats['challenges_detected'] += 1
        elif 'captcha' in line_lower:
            stats['captcha_challenges'] += 1
            stats['challenges_detected'] += 1
        elif 'robot' in line_lower and 'check' in line_lower:
            stats['robot_checks'] += 1
            stats['challenges_detected'] += 1
        elif '代理' in line and ('切换' in line or '更换' in line):
            stats['proxy_switches'] += 1
        elif 'asin' in line_lower and ('处理' in line or 'processing' in line_lower):
            stats['processed_asins'] += 1
    
    stats['total_requests'] = stats['successful_requests'] + stats['failed_requests']
    
    # 显示统计信息
    print("📊 运行统计 (基于最近日志):")
    print(f"  总请求数: {stats['total_requests']}")
    print(f"  成功请求: {stats['successful_requests']}")
    print(f"  失败请求: {stats['failed_requests']}")
    if stats['total_requests'] > 0:
        success_rate = (stats['successful_requests'] / stats['total_requests']) * 100
        print(f"  成功率: {success_rate:.1f}%")
    
    print(f"\n🛡️  验证挑战统计:")
    print(f"  总挑战数: {stats['challenges_detected']}")
    print(f"  手机验证: {stats['phone_verifications']}")
    print(f"  验证码: {stats['captcha_challenges']}")
    print(f"  机器人检查: {stats['robot_checks']}")
    
    print(f"\n🔄 代理统计:")
    print(f"  代理切换次数: {stats['proxy_switches']}")
    
    print(f"\n📦 处理统计:")
    print(f"  处理的ASIN数: {stats['processed_asins']}")
    
    # 分析最近的错误
    print(f"\n❌ 最近的错误:")
    error_count = 0
    for line in recent_logs[-20:]:  # 最近20行
        if 'error' in line.lower() or '错误' in line or 'failed' in line.lower():
            if error_count < 5:  # 只显示最近5个错误
                timestamp = line.split(' - ')[0] if ' - ' in line else 'Unknown'
                error_msg = line.strip()
                print(f"  [{timestamp}] {error_msg}")
                error_count += 1
    
    if error_count == 0:
        print("  ✅ 最近没有发现错误")
    
    print("=" * 60)

def show_recommendations():
    """显示优化建议"""
    print("💡 优化建议:")
    print("1. 如果手机验证过多:")
    print("   - 检查代理质量，使用住宅代理")
    print("   - 降低并发线程数")
    print("   - 增加请求间隔时间")
    
    print("\n2. 如果成功率较低:")
    print("   - 检查代理是否正常工作")
    print("   - 更新User-Agent列表")
    print("   - 检查网络连接")
    
    print("\n3. 如果处理速度慢:")
    print("   - 适当增加并发线程数")
    print("   - 使用更快的代理")
    print("   - 优化网络配置")
    
    print("\n4. 如果验证码识别失败:")
    print("   - 检查amazoncaptcha库是否正常")
    print("   - 确保验证码图片下载成功")
    print("   - 考虑使用其他验证码识别服务")

def real_time_monitor():
    """实时监控模式"""
    print("🔄 实时监控模式")
    print("每10秒更新一次状态，按 Ctrl+C 停止")
    print("=" * 60)
    
    try:
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
            print(f"📅 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            analyze_current_status()
            show_recommendations()
            
            print("\n⏰ 下次更新: 10秒后...")
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n实时监控已停止")

def main():
    """主函数"""
    print("🔍 亚马逊爬虫状态监控工具")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--log':
            # 监控日志文件
            log_file = sys.argv[2] if len(sys.argv) > 2 else 'scraper.log'
            monitor_log_file(log_file)
        elif sys.argv[1] == '--real-time':
            # 实时监控
            real_time_monitor()
        elif sys.argv[1] == '--help':
            print("使用方法:")
            print("  python 监控运行状态.py                    # 分析当前状态")
            print("  python 监控运行状态.py --log [文件名]      # 监控日志文件")
            print("  python 监控运行状态.py --real-time        # 实时监控模式")
            print("  python 监控运行状态.py --help             # 显示帮助")
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
    else:
        # 默认分析当前状态
        analyze_current_status()
        show_recommendations()

if __name__ == "__main__":
    main()
