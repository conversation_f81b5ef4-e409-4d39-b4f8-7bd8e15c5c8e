2025-07-29 15:03:31,533 - INFO - 📊 检查系统状态...
2025-07-29 15:03:31,533 - INFO - 📁 文件检查:
2025-07-29 15:03:31,533 - INFO -    ✅ 优化版本_amazon_scraper.py
2025-07-29 15:03:31,534 - INFO -    ✅ 快速修复_手机验证.py
2025-07-29 15:03:31,534 - INFO -    ✅ proxies.txt
2025-07-29 15:03:31,534 - INFO -    ✅ asin.xlsx
2025-07-29 15:03:31,534 - INFO - 📄 日志文件:
2025-07-29 15:03:31,534 - INFO -    ✅ direct_scraper.log (413 bytes)
2025-07-29 15:03:31,535 - INFO -    ❌ phone_verification_debug.html (0 bytes)
2025-07-29 15:32:14,188 - INFO - 🚀 直接运行版本启动...
2025-07-29 15:32:14,189 - INFO - 📱 应用手机验证修复...
2025-07-29 15:32:14,190 - WARNING - 补丁应用警告: name '__file__' is not defined
2025-07-29 15:32:14,191 - INFO - 💡 继续使用基础功能运行
2025-07-29 15:32:14,191 - INFO - 🔧 导入优化版本爬虫...
2025-07-29 15:32:15,286 - INFO - 🎯 创建爬虫实例...
2025-07-29 15:32:15,288 - INFO - 成功加载 53 个代理
2025-07-29 15:32:15,288 - INFO - 📊 爬虫状态:
2025-07-29 15:32:15,289 - ERROR - 运行失败: 'OptimizedAmazonScraper' object has no attribute 'get_status_summary'
2025-07-29 15:32:15,290 - ERROR - 详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\授权最新文件\shouquan\直接运行版本.py", line 73, in apply_patches_and_run
    status = scraper.get_status_summary()
AttributeError: 'OptimizedAmazonScraper' object has no attribute 'get_status_summary'

