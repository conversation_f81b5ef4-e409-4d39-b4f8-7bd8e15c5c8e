#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试中文页面处理的改进效果
"""

from 筛品牌增强反检测版本 import EnhancedAmazonScraper
import time
import re

def test_chinese_page_handling():
    """测试中文页面处理能力"""
    print("🧪 测试中文页面处理改进效果")
    print("=" * 50)
    
    # 创建爬虫实例
    scraper = EnhancedAmazonScraper()
    
    # 测试用的ASIN - 这些可能会触发中文页面
    test_asins = [
        "B0D8XW9QFF",  # 之前日志中频繁出现中文页面的ASIN
        "B08N5WRWNW",  # 另一个测试ASIN
        "B0D1XD1ZV3",  # AirPods Pro
        "B08C1W5N87",  # Fire TV Stick
        "B07XJ8C8F5"   # Echo Dot
    ]
    
    print(f"📋 准备测试 {len(test_asins)} 个ASIN的中文页面处理")
    print(f"🍪 当前Cookie池大小: {len(scraper.cookie_pool)}")
    print()
    
    results = {
        'success': 0,
        'chinese_converted': 0,
        'failed': 0,
        'conversion_details': []
    }
    
    for i, asin in enumerate(test_asins, 1):
        print(f"🔍 [{i}/{len(test_asins)}] 测试ASIN: {asin}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 获取产品信息
        result = scraper.get_amazon_product_info(asin)
        
        # 记录处理时间
        process_time = time.time() - start_time
        
        # 分析结果
        if result["title"]:
            results['success'] += 1
            print(f"✅ 成功获取产品信息 (耗时: {process_time:.2f}秒)")
            print(f"   📝 标题: {result['title'][:60]}...")
            print(f"   🏷️  品牌: {result['brand']}")
            
            # 检查是否包含中文字符
            if scraper.contains_chinese(result['title']):
                print(f"   ⚠️  标题包含中文字符，可能转换不完全")
            else:
                print(f"   ✅ 标题为英文，转换成功")
                
        else:
            results['failed'] += 1
            print(f"❌ 未能获取产品信息 (耗时: {process_time:.2f}秒)")
        
        # 显示当前统计
        print(f"   📈 累计统计 - 成功: {scraper.success_count}, 失败: {scraper.error_count}")
        print(f"   🔄 请求计数: {scraper.request_count}")
        print()
        
        # 短暂延迟
        if i < len(test_asins):
            time.sleep(2)
    
    # 显示测试总结
    print("🎯 中文页面处理测试完成")
    print("=" * 50)
    print(f"📊 测试结果:")
    print(f"   ✅ 成功: {results['success']}")
    print(f"   ❌ 失败: {results['failed']}")
    print(f"   📈 成功率: {results['success'] / len(test_asins) * 100:.1f}%")
    print(f"   🍪 Cookie池状态: {len(scraper.cookie_pool)} 个Cookie")
    
    # Cookie池详细状态
    print(f"\n🍪 Cookie池详细状态:")
    for i, cookie in enumerate(scraper.cookie_pool, 1):
        total_requests = cookie['success_count'] + cookie['failure_count']
        success_rate = cookie['success_count'] / max(total_requests, 1) * 100
        print(f"   Cookie {i}: 成功率 {success_rate:.1f}% ({cookie['success_count']}/{total_requests})")
    
    return results

def test_location_settings():
    """测试地理位置设置功能"""
    print("\n🧪 测试地理位置设置功能")
    print("=" * 30)
    
    scraper = EnhancedAmazonScraper()
    
    # 测试Cookie设置
    print("🍪 测试美国地理位置Cookie设置...")
    scraper.set_us_location_cookies()
    
    # 检查关键Cookie
    key_cookies = ['lc-main', 'i18n-prefs', 'sp-cdn', 'zipcode', 'country']
    for cookie_name in key_cookies:
        cookie_value = scraper.session.cookies.get(cookie_name)
        if cookie_value:
            print(f"   ✅ {cookie_name}: {cookie_value}")
        else:
            print(f"   ❌ {cookie_name}: 未设置")
    
    # 测试请求头设置
    print("\n📡 测试美国地区请求头设置...")
    headers = scraper.get_us_headers()
    
    key_headers = ['Accept-Language', 'X-Country', 'X-Forwarded-For', 'X-Timezone']
    for header_name in key_headers:
        header_value = headers.get(header_name)
        if header_value:
            print(f"   ✅ {header_name}: {header_value}")
        else:
            print(f"   ❌ {header_name}: 未设置")

def test_chinese_detection():
    """测试中文字符检测功能"""
    print("\n🧪 测试中文字符检测功能")
    print("=" * 30)
    
    scraper = EnhancedAmazonScraper()
    
    test_texts = [
        "Amazon.com: Apple AirPods Pro",  # 纯英文
        "亚马逊：苹果 AirPods Pro",  # 纯中文
        "Amazon.com: 苹果 AirPods Pro",  # 混合
        "Amazon.com: Apple AirPods Pro (中文版)",  # 混合
        "Amazon.com: Apple AirPods Pro"  # 纯英文
    ]
    
    for text in test_texts:
        is_chinese = scraper.contains_chinese(text)
        status = "包含中文" if is_chinese else "纯英文"
        print(f"   {'🇨🇳' if is_chinese else '🇺🇸'} {status}: {text}")

def main():
    """主测试函数"""
    try:
        # 测试地理位置设置
        test_location_settings()
        
        # 测试中文检测
        test_chinese_detection()
        
        # 测试中文页面处理
        results = test_chinese_page_handling()
        
        print("\n🎉 所有测试完成！")
        
        # 给出改进建议
        if results['success'] < len(test_asins) * 0.8:  # 成功率低于80%
            print("\n💡 改进建议:")
            print("   1. 检查代理质量，使用美国IP地址的代理")
            print("   2. 增加Cookie池大小，提供更多有效Cookie")
            print("   3. 考虑增加更多地理位置相关的Cookie设置")
        else:
            print(f"\n✅ 改进效果良好！成功率达到 {results['success'] / len(test_asins) * 100:.1f}%")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
