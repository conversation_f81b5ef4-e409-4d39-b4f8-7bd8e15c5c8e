2025-07-29 14:41:39,106 - INFO - 成功加载 53 个代理
2025-07-29 14:41:39,106 - INFO - 请求 https://www.amazon.com/dp/B071P43VQD 前等待 1.74 秒...
2025-07-29 14:41:43,126 - INFO - 请求成功, 状态码: 200, 用时: 2.28秒
2025-07-29 14:41:43,129 - WARNING - 检测到验证挑战: ChallengeType.IMAGE_CAPTCHA
2025-07-29 14:41:53,428 - ERROR - Selenium处理验证码失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:41:53,429 - ERROR - 验证挑战解决失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:41:56,430 - INFO - 请求 https://www.amazon.com/dp/B077YYF563 前等待 2.46 秒...
2025-07-29 14:41:59,498 - INFO - 请求成功, 状态码: 200, 用时: 0.61秒
2025-07-29 14:41:59,504 - WARNING - 检测到验证挑战: ChallengeType.IMAGE_CAPTCHA
2025-07-29 14:42:05,156 - ERROR - Selenium处理验证码失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:42:05,157 - ERROR - 验证挑战解决失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:42:08,158 - INFO - 请求 https://www.amazon.com/dp/B07CXQ99P3 前等待 1.59 秒...
2025-07-29 14:42:12,331 - INFO - 请求成功, 状态码: 200, 用时: 2.58秒
2025-07-29 14:42:12,506 - INFO - 请求 https://www.amazon.com/s?k=Amberwin&ref=nb_sb_noss 前等待 1.99 秒...
2025-07-29 14:42:23,003 - ERROR - 请求失败: SOCKSHTTPSConnectionPool(host='www.amazon.com', port=443): Max retries exceeded with url: /s?k=Amberwin&ref=nb_sb_noss (Caused by ResponseError('too many 503 error responses'))
2025-07-29 14:42:23,004 - INFO - 请求 https://www.amazon.com/s?k=Amberwin&ref=nb_sb_noss 前等待 2.12 秒...
2025-07-29 14:42:34,646 - INFO - 请求成功, 状态码: 200, 用时: 9.52秒
2025-07-29 14:42:34,725 - INFO - ASIN B07CXQ99P3: 产品信息提取成功
2025-07-29 14:42:37,726 - INFO - 请求 https://www.amazon.com/dp/B0CCNVCKPX 前等待 1.78 秒...
2025-07-29 14:42:40,115 - INFO - 请求成功, 状态码: 200, 用时: 0.61秒
2025-07-29 14:42:40,119 - WARNING - 检测到验证挑战: ChallengeType.IMAGE_CAPTCHA
2025-07-29 14:42:45,804 - ERROR - Selenium处理验证码失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:42:45,804 - ERROR - 验证挑战解决失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:42:48,805 - INFO - 请求 https://www.amazon.com/dp/B082BGM4H3 前等待 2.49 秒...
2025-07-29 14:42:51,902 - INFO - 请求成功, 状态码: 200, 用时: 0.61秒
2025-07-29 14:42:51,904 - WARNING - 检测到验证挑战: ChallengeType.IMAGE_CAPTCHA
2025-07-29 14:43:00,060 - ERROR - Selenium处理验证码失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:43:00,061 - ERROR - 验证挑战解决失败: Message: no such element: Unable to locate element: {"method":"css selector","selector":"img[alt*='captcha' i]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x7ff793d4e935+77845]
	GetHandleVerifier [0x0x7ff793d4e990+77936]
	(No symbol) [0x0x7ff793b09cda]
	(No symbol) [0x0x7ff793b606aa]
	(No symbol) [0x0x7ff793b6095c]
	(No symbol) [0x0x7ff793bb3d07]
	(No symbol) [0x0x7ff793b8890f]
	(No symbol) [0x0x7ff793bb0b07]
	(No symbol) [0x0x7ff793b886a3]
	(No symbol) [0x0x7ff793b51791]
	(No symbol) [0x0x7ff793b52523]
	GetHandleVerifier [0x0x7ff79402684d+3059501]
	GetHandleVerifier [0x0x7ff794020c0d+3035885]
	GetHandleVerifier [0x0x7ff794040400+3164896]
	GetHandleVerifier [0x0x7ff793d68c3e+185118]
	GetHandleVerifier [0x0x7ff793d7054f+216111]
	GetHandleVerifier [0x0x7ff793d572e4+113092]
	GetHandleVerifier [0x0x7ff793d57499+113529]
	GetHandleVerifier [0x0x7ff793d3e298+10616]
	BaseThreadInitThunk [0x0x7ffcf48d7944+20]
	RtlUserThreadStart [0x0x7ffcf4a2ce71+33]

2025-07-29 14:45:30,365 - INFO - 使用代理: 107.174.92.132:48361
2025-07-29 14:45:30,365 - INFO - 🎯 开始尝试触发手机验证...
2025-07-29 14:45:30,365 - INFO - 🔄 尝试 1/20: B017HTXTHC
2025-07-29 14:45:32,459 - INFO -   请求 1: 状态码 200
2025-07-29 14:45:32,461 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:45:33,570 - INFO -   请求 2: 状态码 200
2025-07-29 14:45:33,572 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:45:34,741 - INFO -   请求 3: 状态码 500
2025-07-29 14:45:37,242 - INFO - 🔄 尝试 2/20: B08P9411KS
2025-07-29 14:45:38,083 - INFO -   请求 1: 状态码 500
2025-07-29 14:45:39,200 - INFO -   请求 2: 状态码 500
2025-07-29 14:45:40,318 - INFO -   请求 3: 状态码 500
2025-07-29 14:45:42,819 - INFO - 🔄 尝试 3/20: B097P8Q1G1
2025-07-29 14:45:43,557 - INFO -   请求 1: 状态码 500
2025-07-29 14:45:44,826 - INFO -   请求 2: 状态码 200
2025-07-29 14:45:44,829 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:45:45,975 - INFO -   请求 3: 状态码 500
2025-07-29 14:45:48,476 - INFO - 🔄 尝试 4/20: B0CCNVCKPX
2025-07-29 14:45:49,073 - INFO -   请求 1: 状态码 200
2025-07-29 14:45:49,076 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:45:50,245 - INFO -   请求 2: 状态码 500
2025-07-29 14:45:51,356 - INFO -   请求 3: 状态码 500
2025-07-29 14:45:53,857 - INFO - 🔄 尝试 5/20: B097P8Q1G1
2025-07-29 14:45:54,470 - INFO -   请求 1: 状态码 200
2025-07-29 14:45:54,473 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:45:55,588 - INFO -   请求 2: 状态码 200
2025-07-29 14:45:55,590 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:45:58,107 - INFO -   请求 3: 状态码 200
2025-07-29 14:45:58,182 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:00,683 - INFO - 🔄 尝试 6/20: B0CGCXY9P6
2025-07-29 14:46:02,929 - INFO -   请求 1: 状态码 200
2025-07-29 14:46:03,092 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:05,591 - INFO -   请求 2: 状态码 200
2025-07-29 14:46:05,752 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:08,373 - INFO -   请求 3: 状态码 200
2025-07-29 14:46:08,528 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:11,029 - INFO - 🔄 尝试 7/20: B08P9411KS
2025-07-29 14:46:13,164 - INFO -   请求 1: 状态码 200
2025-07-29 14:46:13,313 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:15,763 - INFO -   请求 2: 状态码 200
2025-07-29 14:46:15,891 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:17,020 - INFO -   请求 3: 状态码 500
2025-07-29 14:46:19,521 - INFO - 🔄 尝试 8/20: B07PKC9K2D
2025-07-29 14:46:21,790 - INFO -   请求 1: 状态码 200
2025-07-29 14:46:21,953 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:24,303 - INFO -   请求 2: 状态码 200
2025-07-29 14:46:24,496 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:27,189 - INFO -   请求 3: 状态码 200
2025-07-29 14:46:27,349 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:29,850 - INFO - 🔄 尝试 9/20: B07PKC9K2D
2025-07-29 14:46:31,806 - INFO -   请求 1: 状态码 200
2025-07-29 14:46:31,968 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:34,361 - INFO -   请求 2: 状态码 200
2025-07-29 14:46:34,552 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:36,991 - INFO -   请求 3: 状态码 200
2025-07-29 14:46:37,150 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:39,652 - INFO - 🔄 尝试 10/20: B0CCNVCKPX
2025-07-29 14:46:42,047 - INFO -   请求 1: 状态码 200
2025-07-29 14:46:42,213 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:45,007 - INFO -   请求 2: 状态码 200
2025-07-29 14:46:45,224 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:47,920 - INFO -   请求 3: 状态码 200
2025-07-29 14:46:48,085 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:50,586 - INFO - 🔄 尝试 11/20: B077YYF563
2025-07-29 14:46:53,015 - INFO -   请求 1: 状态码 200
2025-07-29 14:46:53,169 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:55,888 - INFO -   请求 2: 状态码 200
2025-07-29 14:46:56,092 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:46:57,255 - INFO -   请求 3: 状态码 500
2025-07-29 14:46:59,756 - INFO - 🔄 尝试 12/20: B077YYF563
2025-07-29 14:47:01,839 - INFO -   请求 1: 状态码 200
2025-07-29 14:47:02,008 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:04,997 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:05,187 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:07,912 - INFO -   请求 3: 状态码 200
2025-07-29 14:47:08,081 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:10,581 - INFO - 🔄 尝试 13/20: B08P9411KS
2025-07-29 14:47:12,569 - INFO -   请求 1: 状态码 200
2025-07-29 14:47:12,694 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:15,149 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:15,274 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:17,689 - INFO -   请求 3: 状态码 200
2025-07-29 14:47:17,861 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:20,361 - INFO - 🔄 尝试 14/20: B077YYF563
2025-07-29 14:47:22,459 - INFO -   请求 1: 状态码 200
2025-07-29 14:47:22,623 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:25,084 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:25,250 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:28,009 - INFO -   请求 3: 状态码 200
2025-07-29 14:47:28,205 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:30,707 - INFO - 🔄 尝试 15/20: B01B753NQY
2025-07-29 14:47:31,381 - INFO -   请求 1: 状态码 500
2025-07-29 14:47:32,477 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:32,479 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:47:34,852 - INFO -   请求 3: 状态码 200
2025-07-29 14:47:34,989 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:37,490 - INFO - 🔄 尝试 16/20: B07CXQ99P3
2025-07-29 14:47:39,627 - INFO -   请求 1: 状态码 200
2025-07-29 14:47:39,757 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:42,093 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:42,250 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:43,352 - INFO -   请求 3: 状态码 200
2025-07-29 14:47:43,354 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:47:45,854 - INFO - 🔄 尝试 17/20: B097P8Q1G1
2025-07-29 14:47:46,463 - INFO -   请求 1: 状态码 200
2025-07-29 14:47:46,465 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:47:49,251 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:49,398 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:51,917 - INFO -   请求 3: 状态码 200
2025-07-29 14:47:52,064 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:54,565 - INFO - 🔄 尝试 18/20: B0CGCXY9P6
2025-07-29 14:47:56,724 - INFO -   请求 1: 状态码 200
2025-07-29 14:47:56,929 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:47:59,446 - INFO -   请求 2: 状态码 200
2025-07-29 14:47:59,604 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:48:02,322 - INFO -   请求 3: 状态码 200
2025-07-29 14:48:02,477 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:48:04,978 - INFO - 🔄 尝试 19/20: B0CCNVCKPX
2025-07-29 14:48:05,577 - INFO -   请求 1: 状态码 200
2025-07-29 14:48:05,580 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:08,247 - INFO -   请求 2: 状态码 200
2025-07-29 14:48:08,456 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:48:09,570 - INFO -   请求 3: 状态码 200
2025-07-29 14:48:09,572 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:12,074 - INFO - 🔄 尝试 20/20: B08P9411KS
2025-07-29 14:48:12,676 - INFO -   请求 1: 状态码 200
2025-07-29 14:48:12,678 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:13,787 - INFO -   请求 2: 状态码 500
2025-07-29 14:48:16,129 - INFO -   请求 3: 状态码 200
2025-07-29 14:48:16,257 - INFO - 触发了其他验证: PHONE_VERIFICATION
2025-07-29 14:48:18,759 - WARNING - 未能触发手机验证
2025-07-29 14:48:44,264 - INFO - 使用代理: 107.174.92.132:48361
2025-07-29 14:48:44,264 - INFO - 🎯 开始尝试触发手机验证...
2025-07-29 14:48:44,264 - INFO - 🔄 尝试 1/20: B07PKC9K2D
2025-07-29 14:48:46,636 - INFO -   请求 1: 状态码 200
2025-07-29 14:48:46,639 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:47,768 - INFO -   请求 2: 状态码 200
2025-07-29 14:48:47,770 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:48,959 - INFO -   请求 3: 状态码 500
2025-07-29 14:48:51,459 - INFO - 🔄 尝试 2/20: B017HTXTHC
2025-07-29 14:48:52,107 - INFO -   请求 1: 状态码 500
2025-07-29 14:48:53,193 - INFO -   请求 2: 状态码 200
2025-07-29 14:48:53,195 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:54,279 - INFO -   请求 3: 状态码 200
2025-07-29 14:48:54,281 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:48:56,781 - INFO - 🔄 尝试 3/20: B0CGCXY9P6
2025-07-29 14:48:57,748 - INFO -   请求 1: 状态码 500
2025-07-29 14:48:58,868 - INFO -   请求 2: 状态码 500
2025-07-29 14:48:59,964 - INFO -   请求 3: 状态码 500
2025-07-29 14:49:02,465 - INFO - 🔄 尝试 4/20: B0C1KYJXL6
2025-07-29 14:49:03,123 - INFO -   请求 1: 状态码 500
2025-07-29 14:49:04,210 - INFO -   请求 2: 状态码 200
2025-07-29 14:49:04,213 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:05,333 - INFO -   请求 3: 状态码 500
2025-07-29 14:49:07,834 - INFO - 🔄 尝试 5/20: B077YYF563
2025-07-29 14:49:08,415 - INFO -   请求 1: 状态码 200
2025-07-29 14:49:08,417 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:09,498 - INFO -   请求 2: 状态码 200
2025-07-29 14:49:09,500 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:10,670 - INFO -   请求 3: 状态码 500
2025-07-29 14:49:13,171 - INFO - 🔄 尝试 6/20: B07CXQ99P3
2025-07-29 14:49:13,753 - INFO -   请求 1: 状态码 200
2025-07-29 14:49:13,757 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:14,841 - INFO -   请求 2: 状态码 200
2025-07-29 14:49:14,843 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:15,929 - INFO -   请求 3: 状态码 200
2025-07-29 14:49:15,931 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:18,433 - INFO - 🔄 尝试 7/20: B009GAZG22
2025-07-29 14:49:19,017 - INFO -   请求 1: 状态码 200
2025-07-29 14:49:19,020 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:20,130 - INFO -   请求 2: 状态码 200
2025-07-29 14:49:20,132 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:21,249 - INFO -   请求 3: 状态码 200
2025-07-29 14:49:21,252 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:23,753 - INFO - 🔄 尝试 8/20: B009GAZG22
2025-07-29 14:49:24,423 - INFO -   请求 1: 状态码 500
2025-07-29 14:49:25,516 - INFO -   请求 2: 状态码 500
2025-07-29 14:49:26,613 - INFO -   请求 3: 状态码 500
2025-07-29 14:49:29,113 - INFO - 🔄 尝试 9/20: B077YYF563
2025-07-29 14:49:29,702 - INFO -   请求 1: 状态码 500
2025-07-29 14:49:30,806 - INFO -   请求 2: 状态码 500
2025-07-29 14:49:31,887 - INFO -   请求 3: 状态码 200
2025-07-29 14:49:31,889 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:34,390 - INFO - 🔄 尝试 10/20: B0C1KYJXL6
2025-07-29 14:49:34,968 - INFO -   请求 1: 状态码 200
2025-07-29 14:49:34,970 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:36,105 - INFO -   请求 2: 状态码 500
2025-07-29 14:49:37,243 - INFO -   请求 3: 状态码 200
2025-07-29 14:49:37,245 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:39,746 - INFO - 🔄 尝试 11/20: B088TPQ5P5
2025-07-29 14:49:40,394 - INFO -   请求 1: 状态码 500
2025-07-29 14:49:41,489 - INFO -   请求 2: 状态码 500
2025-07-29 14:49:42,576 - INFO -   请求 3: 状态码 200
2025-07-29 14:49:42,579 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:45,079 - INFO - 🔄 尝试 12/20: B088TPQ5P5
2025-07-29 14:49:45,685 - INFO -   请求 1: 状态码 200
2025-07-29 14:49:45,687 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 14:49:46,783 - INFO -   请求 2: 状态码 500
2025-07-29 14:49:47,948 - INFO -   请求 3: 状态码 200
2025-07-29 14:49:47,951 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:07:41,267 - INFO - 使用代理: 107.174.92.132:48361
2025-07-29 15:07:41,268 - INFO - 🎯 开始尝试触发手机验证...
2025-07-29 15:07:41,268 - INFO - 🔄 尝试 1/20: B0CGCXY9P6
2025-07-29 15:07:43,452 - INFO -   请求 1: 状态码 200
2025-07-29 15:07:43,460 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:07:44,623 - INFO -   请求 2: 状态码 500
2025-07-29 15:07:45,728 - INFO -   请求 3: 状态码 500
2025-07-29 15:07:48,229 - INFO - 🔄 尝试 2/20: B0CGCXY9P6
2025-07-29 15:07:48,831 - INFO -   请求 1: 状态码 500
2025-07-29 15:07:49,924 - INFO -   请求 2: 状态码 500
2025-07-29 15:07:51,026 - INFO -   请求 3: 状态码 200
2025-07-29 15:07:51,032 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:07:53,534 - INFO - 🔄 尝试 3/20: B07PKC9K2D
2025-07-29 15:07:54,177 - INFO -   请求 1: 状态码 200
2025-07-29 15:07:54,182 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:07:55,355 - INFO -   请求 2: 状态码 200
2025-07-29 15:07:55,361 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:07:56,449 - INFO -   请求 3: 状态码 200
2025-07-29 15:07:56,454 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:07:58,955 - INFO - 🔄 尝试 4/20: B0CGCXY9P6
2025-07-29 15:07:59,624 - INFO -   请求 1: 状态码 200
2025-07-29 15:07:59,629 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:00,723 - INFO -   请求 2: 状态码 200
2025-07-29 15:08:00,728 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:01,818 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:01,824 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:04,324 - INFO - 🔄 尝试 5/20: B082BGM4H3
2025-07-29 15:08:04,991 - INFO -   请求 1: 状态码 500
2025-07-29 15:08:06,099 - INFO -   请求 2: 状态码 500
2025-07-29 15:08:07,200 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:07,205 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:09,706 - INFO - 🔄 尝试 6/20: B077YYF563
2025-07-29 15:08:10,364 - INFO -   请求 1: 状态码 500
2025-07-29 15:08:11,455 - INFO -   请求 2: 状态码 200
2025-07-29 15:08:11,461 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:12,562 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:12,567 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:15,068 - INFO - 🔄 尝试 7/20: B07PKC9K2D
2025-07-29 15:08:15,729 - INFO -   请求 1: 状态码 500
2025-07-29 15:08:16,833 - INFO -   请求 2: 状态码 500
2025-07-29 15:08:17,932 - INFO -   请求 3: 状态码 500
2025-07-29 15:08:20,432 - INFO - 🔄 尝试 8/20: B07PKC9K2D
2025-07-29 15:08:21,090 - INFO -   请求 1: 状态码 200
2025-07-29 15:08:21,096 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:23,391 - INFO -   请求 2: 状态码 500
2025-07-29 15:08:25,638 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:25,644 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:28,146 - INFO - 🔄 尝试 9/20: B0C1KYJXL6
2025-07-29 15:08:28,751 - INFO -   请求 1: 状态码 200
2025-07-29 15:08:28,757 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:29,888 - INFO -   请求 2: 状态码 500
2025-07-29 15:08:31,028 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:31,034 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:33,535 - INFO - 🔄 尝试 10/20: B08P9411KS
2025-07-29 15:08:34,137 - INFO -   请求 1: 状态码 500
2025-07-29 15:08:35,222 - INFO -   请求 2: 状态码 200
2025-07-29 15:08:35,227 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:36,390 - INFO -   请求 3: 状态码 500
2025-07-29 15:08:38,892 - INFO - 🔄 尝试 11/20: B0CCNVCKPX
2025-07-29 15:08:39,562 - INFO -   请求 1: 状态码 500
2025-07-29 15:08:40,664 - INFO -   请求 2: 状态码 500
2025-07-29 15:08:41,756 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:41,761 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:44,261 - INFO - 🔄 尝试 12/20: B0CCNVCKPX
2025-07-29 15:08:44,850 - INFO -   请求 1: 状态码 200
2025-07-29 15:08:44,856 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:45,946 - INFO -   请求 2: 状态码 200
2025-07-29 15:08:45,952 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:47,056 - INFO -   请求 3: 状态码 500
2025-07-29 15:08:49,557 - INFO - 🔄 尝试 13/20: B08DL2D9YT
2025-07-29 15:08:50,154 - INFO -   请求 1: 状态码 200
2025-07-29 15:08:50,160 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:08:51,319 - INFO -   请求 2: 状态码 500
2025-07-29 15:08:52,409 - INFO -   请求 3: 状态码 200
2025-07-29 15:08:52,416 - INFO - 触发了其他验证: CLICK_CONTINUE
2025-07-29 15:31:58,471 - INFO - 使用代理: 107.174.92.132:48361
2025-07-29 15:31:58,473 - INFO - 🎯 开始尝试触发手机验证...
2025-07-29 15:31:58,473 - INFO - 🔄 尝试 1/20: B01B753NQY
2025-07-29 15:32:00,611 - INFO -   请求 1: 状态码 500
2025-07-29 15:32:01,706 - INFO -   请求 2: 状态码 200
2025-07-29 15:32:01,713 - INFO - 触发了其他验证: CLICK_CONTINUE
