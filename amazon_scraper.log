2025-07-29 14:21:28,468 - INFO - 成功加载 53 个代理
2025-07-29 14:21:28,474 - INFO - 开始处理 12514 个ASIN
2025-07-29 14:21:28,476 - INFO - 请求 https://www.amazon.com/dp/155040261 前等待 2.33 秒...
2025-07-29 14:21:28,478 - INFO - 请求 https://www.amazon.com/dp/222730218 前等待 1.95 秒...
2025-07-29 14:21:28,480 - INFO - 请求 https://www.amazon.com/dp/181518008 前等待 1.88 秒...
2025-07-29 14:21:28,481 - INFO - 请求 https://www.amazon.com/dp/2769062244 前等待 2.45 秒...
2025-07-29 14:21:28,483 - INFO - 请求 https://www.amazon.com/dp/2991820865 前等待 2.07 秒...
2025-07-29 14:21:28,485 - INFO - 请求 https://www.amazon.com/dp/23549483 前等待 1.88 秒...
2025-07-29 14:21:28,487 - INFO - 请求 https://www.amazon.com/dp/362088691 前等待 2.20 秒...
2025-07-29 14:21:28,488 - INFO - 请求 https://www.amazon.com/dp/3108165932 前等待 2.21 秒...
2025-07-29 14:21:28,489 - INFO - 请求 https://www.amazon.com/dp/1973151300 前等待 2.61 秒...
2025-07-29 14:21:28,490 - INFO - 请求 https://www.amazon.com/dp/5110241250 前等待 2.00 秒...
2025-07-29 14:21:28,491 - INFO - 请求 https://www.amazon.com/dp/550708939 前等待 2.15 秒...
2025-07-29 14:21:28,492 - INFO - 请求 https://www.amazon.com/dp/401147952 前等待 1.54 秒...
2025-07-29 14:21:28,494 - INFO - 请求 https://www.amazon.com/dp/321020939 前等待 1.71 秒...
2025-07-29 14:21:28,495 - INFO - 请求 https://www.amazon.com/dp/2044224426 前等待 1.54 秒...
2025-07-29 14:21:28,496 - INFO - 请求 https://www.amazon.com/dp/10364258757 前等待 2.27 秒...
2025-07-29 14:21:28,497 - INFO - 请求 https://www.amazon.com/dp/14820366883 前等待 2.01 秒...
2025-07-29 14:21:28,498 - INFO - 请求 https://www.amazon.com/dp/312169383 前等待 1.80 秒...
2025-07-29 14:21:28,499 - INFO - 请求 https://www.amazon.com/dp/1809218056 前等待 2.02 秒...
2025-07-29 14:21:28,501 - INFO - 请求 https://www.amazon.com/dp/288723641 前等待 1.23 秒...
2025-07-29 14:21:28,508 - INFO - 请求 https://www.amazon.com/dp/2160609001 前等待 1.54 秒...
2025-07-29 14:21:32,193 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,193 - INFO - 请求 https://www.amazon.com/dp/288723641 前等待 1.47 秒...
2025-07-29 14:21:32,209 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,209 - INFO - 请求 https://www.amazon.com/dp/401147952 前等待 2.05 秒...
2025-07-29 14:21:32,232 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,233 - INFO - 代理进入冷却期: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361, 冷却时间: 300秒
2025-07-29 14:21:32,233 - INFO - 请求 https://www.amazon.com/dp/181518008 前等待 1.89 秒...
2025-07-29 14:21:32,374 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,374 - INFO - 代理进入冷却期: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361, 冷却时间: 300秒
2025-07-29 14:21:32,375 - INFO - 请求 https://www.amazon.com/dp/321020939 前等待 1.17 秒...
2025-07-29 14:21:32,524 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,528 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,529 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,529 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,533 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,533 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,534 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,534 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,535 - INFO - 请求 https://www.amazon.com/dp/222730218 前等待 1.79 秒...
2025-07-29 14:21:32,535 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,536 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,536 - INFO - 请求 https://www.amazon.com/dp/23549483 前等待 2.07 秒...
2025-07-29 14:21:32,536 - INFO - 请求 https://www.amazon.com/dp/312169383 前等待 2.60 秒...
2025-07-29 14:21:32,537 - INFO - 请求 https://www.amazon.com/dp/2160609001 前等待 2.00 秒...
2025-07-29 14:21:32,537 - INFO - 请求 https://www.amazon.com/dp/5110241250 前等待 1.95 秒...
2025-07-29 14:21:32,671 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,672 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,672 - INFO - 请求 https://www.amazon.com/dp/550708939 前等待 2.45 秒...
2025-07-29 14:21:32,694 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:32,695 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,695 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,695 - INFO - 请求 https://www.amazon.com/dp/14820366883 前等待 1.87 秒...
2025-07-29 14:21:32,729 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:32,729 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,729 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,730 - INFO - 请求 https://www.amazon.com/dp/3108165932 前等待 1.38 秒...
2025-07-29 14:21:32,866 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:32,866 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,866 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,867 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:32,867 - INFO - 请求 https://www.amazon.com/dp/2769062244 前等待 2.29 秒...
2025-07-29 14:21:32,892 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,893 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,893 - INFO - 请求 https://www.amazon.com/dp/10364258757 前等待 1.91 秒...
2025-07-29 14:21:32,916 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:32,916 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:32,917 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:32,917 - INFO - 请求 https://www.amazon.com/dp/2044224426 前等待 2.56 秒...
2025-07-29 14:21:33,195 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:33,195 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:33,196 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:33,196 - INFO - 请求 https://www.amazon.com/dp/2991820865 前等待 1.39 秒...
2025-07-29 14:21:33,228 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:33,228 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:33,228 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:33,229 - INFO - 请求 https://www.amazon.com/dp/155040261 前等待 1.05 秒...
2025-07-29 14:21:33,265 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:33,284 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:33,284 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:33,284 - INFO - 请求 https://www.amazon.com/dp/362088691 前等待 2.45 秒...
2025-07-29 14:21:33,626 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:33,626 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:33,627 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:33,627 - INFO - 请求 https://www.amazon.com/dp/1809218056 前等待 1.82 秒...
2025-07-29 14:21:33,962 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:21:34,004 - WARNING - 请求返回状态码: 404
2025-07-29 14:21:34,005 - WARNING - 代理被永久禁用: socks5://1BHVeuZ1WM:65tJbpVstb@107.174.92.132:48361
2025-07-29 14:21:34,005 - INFO - 请求 https://www.amazon.com/dp/1973151300 前等待 2.05 秒...
2025-07-29 14:21:38,767 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000002328976B460>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/321020939
2025-07-29 14:21:38,817 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000002328976B700>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/288723641
2025-07-29 14:21:39,268 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000002328976BB50>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/3108165932
2025-07-29 14:21:39,274 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CC040>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/181518008
2025-07-29 14:21:39,409 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CC580>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/401147952
2025-07-29 14:21:39,433 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CCA60>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/155040261
2025-07-29 14:21:39,478 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CCF40>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/222730218
2025-07-29 14:21:39,645 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CD420>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/5110241250
2025-07-29 14:21:39,783 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CD900>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/2160609001
2025-07-29 14:21:39,838 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CE2C0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/2991820865
2025-07-29 14:21:39,838 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CDDE0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/14820366883
2025-07-29 14:21:39,879 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CE7A0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/23549483
2025-07-29 14:21:39,961 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CEC80>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/10364258757
2025-07-29 14:21:40,273 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CF160>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/550708939
2025-07-29 14:21:40,287 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CF640>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/312169383
2025-07-29 14:21:40,313 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897CFB20>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/2769062244
2025-07-29 14:21:40,601 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F8040>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/1809218056
2025-07-29 14:21:40,632 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F8520>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/2044224426
2025-07-29 14:21:40,981 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F8A60>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/362088691
2025-07-29 14:21:41,209 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F9060>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/1973151300
2025-07-29 14:21:45,920 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F9510>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/321020939
2025-07-29 14:21:45,970 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F9840>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/288723641
2025-07-29 14:21:46,421 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F9BA0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/3108165932
2025-07-29 14:21:46,422 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897F9F00>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/181518008
2025-07-29 14:21:46,572 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FA260>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/401147952
2025-07-29 14:21:46,582 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FA5C0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/155040261
2025-07-29 14:21:46,736 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FA920>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/222730218
2025-07-29 14:21:46,796 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FAC80>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/5110241250
2025-07-29 14:21:46,939 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FAFE0>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/2160609001
2025-07-29 14:21:46,995 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FB460>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/2991820865
2025-07-29 14:21:46,996 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FB610>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/14820366883
2025-07-29 14:21:47,037 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FBA00>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/23549483
2025-07-29 14:21:47,115 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000232897FBD60>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/10364258757
2025-07-29 14:21:47,426 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000002328982C100>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/550708939
2025-07-29 14:21:47,450 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x000002328982C460>: Failed to establish a new connection: SOCKS5 proxy server sent invalid data')': /dp/312169383
2025-07-29 14:22:11,511 - INFO - 成功加载 53 个代理
2025-07-29 14:22:11,514 - INFO - 开始处理 1608 个ASIN
2025-07-29 14:22:11,516 - INFO - 请求 https://www.amazon.com/dp/B000HJUXMO 前等待 1.82 秒...
2025-07-29 14:22:11,517 - INFO - 请求 https://www.amazon.com/dp/B07G3KS4JK 前等待 2.19 秒...
2025-07-29 14:22:11,518 - INFO - 请求 https://www.amazon.com/dp/B009OFW11I 前等待 1.53 秒...
2025-07-29 14:22:11,520 - INFO - 请求 https://www.amazon.com/dp/B097XQ49D4 前等待 1.46 秒...
2025-07-29 14:22:11,522 - INFO - 请求 https://www.amazon.com/dp/B00P9UFHVI 前等待 2.40 秒...
2025-07-29 14:22:11,523 - INFO - 请求 https://www.amazon.com/dp/B006GGMRTY 前等待 1.64 秒...
2025-07-29 14:22:11,524 - INFO - 请求 https://www.amazon.com/dp/B07GYZW122 前等待 1.61 秒...
2025-07-29 14:22:11,525 - INFO - 请求 https://www.amazon.com/dp/B075NTZR97 前等待 1.81 秒...
2025-07-29 14:22:11,526 - INFO - 请求 https://www.amazon.com/dp/B09J1YB5LX 前等待 1.94 秒...
2025-07-29 14:22:11,527 - INFO - 请求 https://www.amazon.com/dp/B091HRSGGT 前等待 2.18 秒...
2025-07-29 14:22:11,528 - INFO - 请求 https://www.amazon.com/dp/B009ESWLB0 前等待 1.77 秒...
2025-07-29 14:22:11,531 - INFO - 请求 https://www.amazon.com/dp/B0DKXRBPSP 前等待 2.39 秒...
2025-07-29 14:22:11,533 - INFO - 请求 https://www.amazon.com/dp/B07PCQGWDY 前等待 2.21 秒...
2025-07-29 14:22:11,534 - INFO - 请求 https://www.amazon.com/dp/B0B2QFGL5P 前等待 2.09 秒...
2025-07-29 14:22:11,535 - INFO - 请求 https://www.amazon.com/dp/B0DGX9J324 前等待 2.03 秒...
2025-07-29 14:22:11,536 - INFO - 请求 https://www.amazon.com/dp/B0BC9QVRR9 前等待 1.65 秒...
2025-07-29 14:22:11,537 - INFO - 请求 https://www.amazon.com/dp/B0C7KS82RL 前等待 2.21 秒...
2025-07-29 14:22:11,538 - INFO - 请求 https://www.amazon.com/dp/B08DKS66PT 前等待 1.86 秒...
2025-07-29 14:22:11,539 - INFO - 请求 https://www.amazon.com/dp/B0CXTM2MJM 前等待 1.65 秒...
2025-07-29 14:22:11,552 - INFO - 请求 https://www.amazon.com/dp/B01M7PJ0VG 前等待 1.51 秒...
2025-07-29 14:22:16,685 - INFO - 请求成功, 状态码: 200, 用时: 3.52秒
2025-07-29 14:22:16,712 - INFO - 请求成功, 状态码: 200, 用时: 3.52秒
2025-07-29 14:22:16,738 - INFO - 请求成功, 状态码: 200, 用时: 3.60秒
2025-07-29 14:22:16,774 - INFO - 请求成功, 状态码: 200, 用时: 3.44秒
2025-07-29 14:22:17,023 - INFO - 请求成功, 状态码: 200, 用时: 3.93秒
2025-07-29 14:22:17,071 - INFO - 请求成功, 状态码: 200, 用时: 4.02秒
2025-07-29 14:22:17,321 - INFO - 请求成功, 状态码: 200, 用时: 3.99秒
2025-07-29 14:22:17,358 - INFO - 请求成功, 状态码: 200, 用时: 4.38秒
2025-07-29 14:22:17,956 - INFO - 请求成功, 状态码: 200, 用时: 4.21秒
2025-07-29 14:22:18,082 - INFO - 请求成功, 状态码: 200, 用时: 4.37秒
2025-07-29 14:22:18,246 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:18,393 - INFO - 请求成功, 状态码: 200, 用时: 5.20秒
2025-07-29 14:22:18,626 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:18,667 - INFO - 请求成功, 状态码: 200, 用时: 5.37秒
2025-07-29 14:22:18,899 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:18,905 - INFO - 请求成功, 状态码: 200, 用时: 5.43秒
2025-07-29 14:22:19,472 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:19,535 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:19,835 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:19,853 - INFO - 请求成功, 状态码: 200, 用时: 5.93秒
2025-07-29 14:22:19,893 - INFO - 请求成功, 状态码: 200, 用时: 6.49秒
2025-07-29 14:22:20,072 - INFO - 请求成功, 状态码: 200, 用时: 6.37秒
2025-07-29 14:22:20,120 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:20,387 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:20,495 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:20,704 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:20,792 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:20,913 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:20,913 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:21,055 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:21,328 - INFO - 请求 https://www.amazon.com/dp/B0B8DPWK7C 前等待 1.70 秒...
2025-07-29 14:22:21,358 - INFO - 请求成功, 状态码: 200, 用时: 7.74秒
2025-07-29 14:22:21,375 - WARNING - ASIN B07GYZW122 处理失败
2025-07-29 14:22:21,388 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:21,394 - INFO - 请求 https://www.amazon.com/dp/B0D3HJYFR3 前等待 2.30 秒...
2025-07-29 14:22:21,638 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:21,667 - INFO - 请求成功, 状态码: 200, 用时: 7.74秒
2025-07-29 14:22:21,755 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:21,756 - INFO - 请求 https://www.amazon.com/dp/B0CMD3MZSD 前等待 1.52 秒...
2025-07-29 14:22:21,814 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:22,296 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:22,446 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:22,647 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:22,754 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:22,866 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:22,912 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:23,175 - WARNING - ASIN B006GGMRTY 处理失败
2025-07-29 14:22:23,355 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:23,413 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:23,572 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:23,617 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:23,730 - INFO - 请求成功, 状态码: 200, 用时: 9.98秒
2025-07-29 14:22:23,854 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:23,860 - INFO - 请求 https://www.amazon.com/dp/B0CMD2P2CN 前等待 2.84 秒...
2025-07-29 14:22:24,018 - INFO - 请求 https://www.amazon.com/dp/B094Y3CQC3 前等待 2.33 秒...
2025-07-29 14:22:24,054 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,082 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,132 - INFO - 请求 https://www.amazon.com/dp/B0DYJRXF6F 前等待 2.42 秒...
2025-07-29 14:22:24,352 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,415 - INFO - 请求成功, 状态码: 200, 用时: 10.85秒
2025-07-29 14:22:24,527 - INFO - 请求 https://www.amazon.com/dp/B0BHYPHBD1 前等待 1.99 秒...
2025-07-29 14:22:24,601 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,629 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,641 - WARNING - ASIN B0BC9QVRR9 处理失败
2025-07-29 14:22:24,663 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,849 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,965 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:24,995 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:25,122 - INFO - 请求 https://www.amazon.com/dp/B09P7VSGW7 前等待 1.61 秒...
2025-07-29 14:22:25,216 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:25,427 - INFO - 请求 https://www.amazon.com/dp/B0882M9ZQR 前等待 1.79 秒...
2025-07-29 14:22:25,452 - INFO - 请求 https://www.amazon.com/dp/B07L2QB3FZ 前等待 2.09 秒...
2025-07-29 14:22:25,535 - INFO - 请求 https://www.amazon.com/dp/B08DM8Y2Q9 前等待 2.07 秒...
2025-07-29 14:22:25,679 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:25,679 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:25,709 - INFO - 请求 https://www.amazon.com/dp/B07CMLCNJL 前等待 1.88 秒...
2025-07-29 14:22:25,716 - INFO - 请求 https://www.amazon.com/dp/B0C9ZT2VZ2 前等待 2.05 秒...
2025-07-29 14:22:25,783 - WARNING - ASIN B000HJUXMO 处理失败
2025-07-29 14:22:25,832 - INFO - 请求 https://www.amazon.com/dp/B01KLGJFTY 前等待 2.19 秒...
2025-07-29 14:22:25,926 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,041 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,106 - INFO - 请求 https://www.amazon.com/dp/B07PPJTWD4 前等待 2.19 秒...
2025-07-29 14:22:26,242 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,285 - INFO - 请求 https://www.amazon.com/dp/B00T6I69XY 前等待 1.81 秒...
2025-07-29 14:22:26,356 - INFO - 请求 https://www.amazon.com/dp/B08DM4CQZN 前等待 1.99 秒...
2025-07-29 14:22:26,411 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,440 - WARNING - ASIN B01M7PJ0VG 处理失败
2025-07-29 14:22:26,457 - INFO - 请求成功, 状态码: 200, 用时: 2.86秒
2025-07-29 14:22:26,469 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,732 - INFO - 请求 https://www.amazon.com/dp/B09KBZTZX2 前等待 2.01 秒...
2025-07-29 14:22:26,932 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,949 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:26,949 - WARNING - ASIN B075NTZR97 处理失败
2025-07-29 14:22:26,956 - INFO - 请求 https://www.amazon.com/dp/B07JFK95ZD 前等待 2.24 秒...
2025-07-29 14:22:27,185 - WARNING - ASIN B07PCQGWDY 处理失败
2025-07-29 14:22:27,351 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:27,370 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:27,382 - WARNING - ASIN B097XQ49D4 处理失败
2025-07-29 14:22:27,383 - INFO - 请求 https://www.amazon.com/dp/B08SJQ8138 前等待 1.69 秒...
2025-07-29 14:22:27,438 - WARNING - ASIN B009OFW11I 处理失败
2025-07-29 14:22:27,473 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:27,473 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:27,474 - INFO - 请求 https://www.amazon.com/dp/B0885WP364 前等待 2.05 秒...
2025-07-29 14:22:27,474 - WARNING - ASIN B00P9UFHVI 处理失败
2025-07-29 14:22:27,476 - INFO - 请求成功, 状态码: 200, 用时: 2.90秒
2025-07-29 14:22:27,494 - INFO - 请求成功, 状态码: 200, 用时: 2.68秒
2025-07-29 14:22:27,506 - WARNING - ASIN B091HRSGGT 处理失败
2025-07-29 14:22:27,800 - WARNING - ASIN B0CXTM2MJM 处理失败
2025-07-29 14:22:27,951 - WARNING - ASIN B009ESWLB0 处理失败
2025-07-29 14:22:28,093 - WARNING - ASIN B09J1YB5LX 处理失败
2025-07-29 14:22:28,378 - WARNING - ASIN B07G3KS4JK 处理失败
2025-07-29 14:22:28,585 - WARNING - ASIN B0DKXRBPSP 处理失败
2025-07-29 14:22:28,591 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:28,626 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:28,631 - INFO - 请求 https://www.amazon.com/dp/B08FZN5JW4 前等待 2.30 秒...
2025-07-29 14:22:28,684 - WARNING - ASIN B0B2QFGL5P 处理失败
2025-07-29 14:22:28,712 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:28,713 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:28,713 - INFO - 请求 https://www.amazon.com/dp/B01JXOG2AK 前等待 1.82 秒...
2025-07-29 14:22:28,715 - WARNING - ASIN B08DKS66PT 处理失败
2025-07-29 14:22:28,716 - WARNING - ASIN B0C7KS82RL 处理失败
2025-07-29 14:22:28,717 - WARNING - ASIN B0DGX9J324 处理失败
2025-07-29 14:22:28,718 - WARNING - ASIN B0B8DPWK7C 处理失败
2025-07-29 14:22:28,719 - WARNING - ASIN B0CMD3MZSD 处理失败
2025-07-29 14:22:28,720 - WARNING - ASIN B0D3HJYFR3 处理失败
2025-07-29 14:22:29,606 - INFO - 请求成功, 状态码: 200, 用时: 2.28秒
2025-07-29 14:22:29,930 - INFO - 请求成功, 状态码: 200, 用时: 2.62秒
2025-07-29 14:22:30,190 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:30,198 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:30,204 - INFO - 请求 https://www.amazon.com/dp/B0B51XTBRK 前等待 2.06 秒...
2025-07-29 14:22:30,210 - WARNING - ASIN B0BHYPHBD1 处理失败
2025-07-29 14:22:30,252 - INFO - 请求成功, 状态码: 200, 用时: 2.25秒
2025-07-29 14:22:30,504 - INFO - 请求成功, 状态码: 200, 用时: 2.83秒
2025-07-29 14:22:30,688 - INFO - 请求成功, 状态码: 200, 用时: 2.54秒
2025-07-29 14:22:30,693 - INFO - 请求成功, 状态码: 200, 用时: 3.29秒
2025-07-29 14:22:30,746 - INFO - 请求成功, 状态码: 200, 用时: 3.15秒
2025-07-29 14:22:31,160 - INFO - 请求成功, 状态码: 200, 用时: 3.35秒
2025-07-29 14:22:31,426 - INFO - 请求成功, 状态码: 200, 用时: 3.23秒
2025-07-29 14:22:32,003 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:32,080 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:32,165 - INFO - 请求成功, 状态码: 200, 用时: 4.07秒
2025-07-29 14:22:32,165 - INFO - 请求 https://www.amazon.com/dp/B018GRP1UM 前等待 1.96 秒...
2025-07-29 14:22:32,184 - WARNING - ASIN B094Y3CQC3 处理失败
2025-07-29 14:22:32,435 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:32,535 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:32,593 - INFO - 请求 https://www.amazon.com/dp/B0BRNRJNHG 前等待 1.71 秒...
2025-07-29 14:22:32,843 - INFO - 请求成功, 状态码: 200, 用时: 4.61秒
2025-07-29 14:22:33,168 - WARNING - ASIN B07CMLCNJL 处理失败
2025-07-29 14:22:33,913 - INFO - 请求成功, 状态码: 200, 用时: 5.41秒
2025-07-29 14:22:34,086 - INFO - 请求成功, 状态码: 200, 用时: 5.64秒
2025-07-29 14:22:34,338 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:34,380 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:34,403 - INFO - 请求 https://www.amazon.com/dp/B087NDG1HH 前等待 2.05 秒...
2025-07-29 14:22:34,474 - WARNING - ASIN B08DM8Y2Q9 处理失败
2025-07-29 14:22:34,577 - WARNING - 检测到验证挑战: ChallengeType.ROBOT_CHECK
2025-07-29 14:22:34,709 - WARNING - 检测到机器人检查，建议更换代理
2025-07-29 14:22:34,715 - INFO - 请求成功, 状态码: 200, 用时: 3.76秒
2025-07-29 14:22:34,750 - ERROR - 验证挑战解决失败: 需要更换代理
2025-07-29 14:22:34,821 - INFO - 请求 https://www.amazon.com/dp/B077YYF563 前等待 2.38 秒...
2025-07-29 14:22:35,251 - WARNING - ASIN B09P7VSGW7 处理失败
2025-07-29 14:22:35,350 - INFO - 请求成功, 状态码: 200, 用时: 6.60秒
2025-07-29 14:22:35,486 - WARNING - 检测到验证挑战: ChallengeType.ROBOT_CHECK
2025-07-29 14:22:35,499 - WARNING - 检测到机器人检查，建议更换代理
2025-07-29 14:22:35,499 - ERROR - 验证挑战解决失败: 需要更换代理
2025-07-29 14:22:35,505 - INFO - 请求 https://www.amazon.com/dp/B099JWWK13 前等待 1.96 秒...
2025-07-29 14:22:35,735 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:35,758 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:35,760 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:35,799 - WARNING - ASIN B0882M9ZQR 处理失败
2025-07-29 14:22:35,838 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:35,844 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:36,007 - INFO - 请求 https://www.amazon.com/dp/B09WJKH46Z 前等待 2.07 秒...
2025-07-29 14:22:36,074 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:36,103 - INFO - 请求成功, 状态码: 200, 用时: 5.50秒
2025-07-29 14:22:36,127 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:36,203 - INFO - 请求 https://www.amazon.com/dp/B077JNLQ3S 前等待 2.38 秒...
2025-07-29 14:22:36,278 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:36,343 - WARNING - ASIN B0CMD2P2CN 处理失败
2025-07-29 14:22:36,419 - INFO - 请求成功, 状态码: 200, 用时: 6.90秒
2025-07-29 14:22:36,827 - INFO - 请求成功, 状态码: 200, 用时: 7.74秒
2025-07-29 14:22:36,919 - INFO - 请求成功, 状态码: 200, 用时: 8.49秒
2025-07-29 14:22:36,955 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,164 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:37,181 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,206 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,232 - WARNING - Connection pool is full, discarding connection: www.amazon.com. Connection pool size: 10
2025-07-29 14:22:37,272 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,555 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,579 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,631 - WARNING - ASIN B0C9ZT2VZ2 处理失败
2025-07-29 14:22:37,830 - INFO - 请求成功, 状态码: 200, 用时: 8.50秒
2025-07-29 14:22:37,848 - INFO - 请求 https://www.amazon.com/dp/B0BGS3P8QX 前等待 2.15 秒...
2025-07-29 14:22:37,884 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:37,932 - INFO - 请求成功, 状态码: 200, 用时: 5.66秒
2025-07-29 14:22:38,020 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:38,067 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:38,152 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:38,199 - INFO - 请求 https://www.amazon.com/dp/B09XP51PLW 前等待 2.71 秒...
2025-07-29 14:22:38,270 - INFO - 请求成功, 状态码: 200, 用时: 3.83秒
2025-07-29 14:22:38,317 - INFO - 请求 https://www.amazon.com/dp/B09RGTWP5C 前等待 1.51 秒...
2025-07-29 14:22:38,463 - INFO - 请求 https://www.amazon.com/dp/B01B753NQY 前等待 2.11 秒...
2025-07-29 14:22:38,640 - WARNING - ASIN B0DYJRXF6F 处理失败
2025-07-29 14:22:38,640 - INFO - 请求 https://www.amazon.com/dp/B0B4BF9P8P 前等待 1.99 秒...
2025-07-29 14:22:38,740 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:39,313 - INFO - 请求 https://www.amazon.com/dp/B09JB519BM 前等待 1.97 秒...
2025-07-29 14:22:39,361 - INFO - 请求成功, 状态码: 200, 用时: 5.08秒
2025-07-29 14:22:39,466 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:39,518 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:39,524 - INFO - 请求 https://www.amazon.com/dp/B0CPLZSMWB 前等待 2.38 秒...
2025-07-29 14:22:39,624 - WARNING - ASIN B00T6I69XY 处理失败
2025-07-29 14:22:40,371 - WARNING - ASIN B07L2QB3FZ 处理失败
2025-07-29 14:22:40,816 - WARNING - ASIN B07PPJTWD4 处理失败
2025-07-29 14:22:40,850 - INFO - 请求成功, 状态码: 200, 用时: 4.34秒
2025-07-29 14:22:41,564 - WARNING - ASIN B08DM4CQZN 处理失败
2025-07-29 14:22:41,638 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:41,795 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:41,825 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:41,897 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:41,959 - INFO - 请求 https://www.amazon.com/dp/B0CCNVCKPX 前等待 1.64 秒...
2025-07-29 14:22:42,082 - WARNING - ASIN B08FZN5JW4 处理失败
2025-07-29 14:22:42,097 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,158 - INFO - 请求 https://www.amazon.com/dp/B009GAZG22 前等待 2.23 秒...
2025-07-29 14:22:42,384 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,470 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,554 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,560 - WARNING - ASIN B09KBZTZX2 处理失败
2025-07-29 14:22:42,573 - INFO - 请求 https://www.amazon.com/dp/B07PKC9K2D 前等待 1.95 秒...
2025-07-29 14:22:42,596 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,619 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,641 - INFO - 请求成功, 状态码: 200, 用时: 5.08秒
2025-07-29 14:22:42,808 - INFO - 请求 https://www.amazon.com/dp/B017HTXTHC 前等待 1.97 秒...
2025-07-29 14:22:42,819 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:42,831 - INFO - 请求成功, 状态码: 200, 用时: 5.59秒
2025-07-29 14:22:42,914 - INFO - 请求 https://www.amazon.com/dp/B0C1KYJXL6 前等待 1.77 秒...
2025-07-29 14:22:42,996 - INFO - 请求成功, 状态码: 200, 用时: 4.56秒
2025-07-29 14:22:43,008 - WARNING - ASIN B01KLGJFTY 处理失败
2025-07-29 14:22:43,049 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,142 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,226 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,242 - INFO - 请求 https://www.amazon.com/dp/B0CGCXY9P6 前等待 1.71 秒...
2025-07-29 14:22:43,265 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,287 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,294 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,341 - INFO - 请求成功, 状态码: 200, 用时: 3.76秒
2025-07-29 14:22:43,346 - INFO - 请求 https://www.amazon.com/dp/B088TPQ5P5 前等待 1.63 秒...
2025-07-29 14:22:43,449 - INFO - 请求 https://www.amazon.com/dp/B097P8Q1G1 前等待 2.40 秒...
2025-07-29 14:22:43,480 - WARNING - ASIN B08SJQ8138 处理失败
2025-07-29 14:22:43,561 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:43,852 - INFO - 请求 https://www.amazon.com/dp/B0BRQM83FY 前等待 2.02 秒...
2025-07-29 14:22:44,118 - WARNING - ASIN B01JXOG2AK 处理失败
2025-07-29 14:22:44,346 - INFO - 请求成功, 状态码: 200, 用时: 3.09秒
2025-07-29 14:22:44,768 - INFO - 请求成功, 状态码: 200, 用时: 4.28秒
2025-07-29 14:22:44,792 - WARNING - ASIN B0B51XTBRK 处理失败
2025-07-29 14:22:45,015 - INFO - 请求成功, 状态码: 200, 用时: 3.66秒
2025-07-29 14:22:45,085 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:45,225 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:45,248 - INFO - 请求 https://www.amazon.com/dp/B0BRQMBYQC 前等待 1.71 秒...
2025-07-29 14:22:45,253 - INFO - 请求成功, 状态码: 200, 用时: 3.91秒
2025-07-29 14:22:45,254 - WARNING - ASIN B07JFK95ZD 处理失败
2025-07-29 14:22:45,389 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:45,521 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:45,569 - INFO - 请求 https://www.amazon.com/dp/B01BUJRNEA 前等待 1.73 秒...
2025-07-29 14:22:45,801 - WARNING - ASIN B0885WP364 处理失败
2025-07-29 14:22:45,820 - INFO - 请求成功, 状态码: 200, 用时: 5.25秒
2025-07-29 14:22:46,114 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:46,125 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:46,142 - INFO - 请求 https://www.amazon.com/dp/B0CGM22H64 前等待 1.87 秒...
2025-07-29 14:22:46,151 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:46,217 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:46,252 - INFO - 请求 https://www.amazon.com/dp/B099YV4LHL 前等待 1.76 秒...
2025-07-29 14:22:46,285 - WARNING - ASIN B0BRNRJNHG 处理失败
2025-07-29 14:22:46,554 - INFO - 请求成功, 状态码: 200, 用时: 4.42秒
2025-07-29 14:22:46,877 - WARNING - ASIN B018GRP1UM 处理失败
2025-07-29 14:22:47,155 - INFO - 请求成功, 状态码: 200, 用时: 5.60秒
2025-07-29 14:22:47,718 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:47,746 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:47,794 - WARNING - ASIN B087NDG1HH 处理失败
2025-07-29 14:22:47,847 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:47,882 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:47,922 - INFO - 请求 https://www.amazon.com/dp/B07NT4JNWB 前等待 2.29 秒...
2025-07-29 14:22:48,071 - INFO - 请求 https://www.amazon.com/dp/B08KTB8GJP 前等待 1.80 秒...
2025-07-29 14:22:48,127 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:48,128 - WARNING - ASIN B09WJKH46Z 处理失败
2025-07-29 14:22:48,298 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:48,331 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:48,446 - INFO - 请求 https://www.amazon.com/dp/B08K8M54M8 前等待 2.14 秒...
2025-07-29 14:22:48,475 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:48,540 - INFO - 请求成功, 状态码: 200, 用时: 4.56秒
2025-07-29 14:22:48,586 - INFO - 请求成功, 状态码: 200, 用时: 3.53秒
2025-07-29 14:22:48,603 - WARNING - ASIN B077YYF563 处理失败
2025-07-29 14:22:48,621 - INFO - 请求 https://www.amazon.com/dp/B099HNF76R 前等待 2.28 秒...
2025-07-29 14:22:48,649 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:48,685 - INFO - 请求成功, 状态码: 200, 用时: 3.63秒
2025-07-29 14:22:48,720 - INFO - 请求成功, 状态码: 200, 用时: 3.95秒
2025-07-29 14:22:48,736 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:48,742 - INFO - 请求成功, 状态码: 200, 用时: 4.00秒
2025-07-29 14:22:48,760 - INFO - 请求成功, 状态码: 200, 用时: 3.77秒
2025-07-29 14:22:48,922 - INFO - 请求 https://www.amazon.com/dp/B09MB91D3D 前等待 1.92 秒...
2025-07-29 14:22:48,979 - WARNING - ASIN B077JNLQ3S 处理失败
2025-07-29 14:22:49,455 - INFO - 请求成功, 状态码: 200, 用时: 3.58秒
2025-07-29 14:22:50,037 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:50,124 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:50,136 - INFO - 请求 https://www.amazon.com/dp/B07CXRKKNK 前等待 1.95 秒...
2025-07-29 14:22:50,246 - WARNING - ASIN B099JWWK13 处理失败
2025-07-29 14:22:50,294 - INFO - 请求成功, 状态码: 200, 用时: 4.11秒
2025-07-29 14:22:51,012 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:51,018 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:51,042 - WARNING - ASIN B01B753NQY 处理失败
2025-07-29 14:22:51,294 - INFO - 请求 https://www.amazon.com/dp/B08KR34JHY 前等待 2.44 秒...
2025-07-29 14:22:51,402 - INFO - 请求成功, 状态码: 200, 用时: 6.10秒
2025-07-29 14:22:51,620 - INFO - 请求成功, 状态码: 200, 用时: 4.31秒
2025-07-29 14:22:51,698 - INFO - 请求成功, 状态码: 200, 用时: 4.51秒
2025-07-29 14:22:51,948 - WARNING - ASIN B09JB519BM 处理失败
2025-07-29 14:22:52,613 - WARNING - ASIN B0BGS3P8QX 处理失败
2025-07-29 14:22:53,062 - INFO - 请求成功, 状态码: 200, 用时: 5.03秒
2025-07-29 14:22:53,201 - WARNING - ASIN B0B4BF9P8P 处理失败
2025-07-29 14:22:53,303 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:53,308 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:53,373 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:53,471 - INFO - 请求 https://www.amazon.com/dp/B0C62K5CRX 前等待 1.90 秒...
2025-07-29 14:22:53,565 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:53,873 - INFO - 请求 https://www.amazon.com/dp/B071P43VQD 前等待 2.40 秒...
2025-07-29 14:22:53,879 - WARNING - ASIN B09RGTWP5C 处理失败
2025-07-29 14:22:54,023 - INFO - 请求成功, 状态码: 200, 用时: 5.92秒
2025-07-29 14:22:54,024 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,354 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,418 - INFO - 请求 https://www.amazon.com/dp/B07CXQ99P3 前等待 2.02 秒...
2025-07-29 14:22:54,718 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,774 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,888 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,906 - WARNING - ASIN B0CPLZSMWB 处理失败
2025-07-29 14:22:54,909 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,934 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:54,975 - INFO - 请求 https://www.amazon.com/dp/B082BGM4H3 前等待 2.24 秒...
2025-07-29 14:22:55,241 - ERROR - 验证挑战解决失败: 不支持的验证类型: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:55,300 - INFO - 请求成功, 状态码: 200, 用时: 5.22秒
2025-07-29 14:22:55,328 - INFO - 请求 https://www.amazon.com/dp/B072KKGC1Y 前等待 1.71 秒...
2025-07-29 14:22:55,362 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:55,626 - INFO - 请求 https://www.amazon.com/dp/B08DL2D9YT 前等待 2.16 秒...
2025-07-29 14:22:55,711 - INFO - 请求成功, 状态码: 200, 用时: 4.67秒
2025-07-29 14:22:55,734 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 14:22:55,788 - WARNING - ASIN B09XP51PLW 处理失败
2025-07-29 14:22:55,812 - WARNING - 检测到验证挑战: ChallengeType.PHONE_VERIFICATION
2025-07-29 15:32:40,337 - INFO - ✅ 增强手机验证处理补丁应用成功
