#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级反检测系统 - 集成多种反爬虫技术
结合现有的手机验证破解功能，提供更强大的反检测能力
"""

import requests
import time
import random
import json
import base64
import hashlib
import hmac
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
import undetected_chrome as uc
from fake_useragent import UserAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_anti_detection.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedAntiDetectionSystem:
    """高级反检测系统"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.driver = None
        self.fingerprint_data = {}
        self.setup_session()
    
    def setup_session(self):
        """设置高级会话配置"""
        # 随机User-Agent
        user_agent = self.ua.random
        
        # 高级请求头
        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }
        
        self.session.headers.update(headers)
        
        # 设置SSL和TLS配置
        self.session.verify = True
        
        logger.info(f"🔧 设置高级会话配置完成，User-Agent: {user_agent[:50]}...")
    
    def generate_browser_fingerprint(self):
        """生成浏览器指纹"""
        fingerprint = {
            'screen': {
                'width': random.choice([1920, 1366, 1536, 1440, 1280]),
                'height': random.choice([1080, 768, 864, 900, 720]),
                'colorDepth': 24,
                'pixelDepth': 24
            },
            'timezone': random.choice([-480, -420, -360, -300, -240, -180, -120, 0, 60, 120]),
            'language': random.choice(['en-US', 'en-GB', 'zh-CN', 'zh-TW']),
            'platform': random.choice(['Win32', 'MacIntel', 'Linux x86_64']),
            'cookieEnabled': True,
            'doNotTrack': random.choice(['1', '0', None]),
            'plugins': self._generate_plugin_list(),
            'webgl': self._generate_webgl_info(),
            'canvas': self._generate_canvas_fingerprint(),
            'audio': self._generate_audio_fingerprint()
        }
        
        self.fingerprint_data = fingerprint
        logger.info("🔍 生成浏览器指纹完成")
        return fingerprint
    
    def _generate_plugin_list(self):
        """生成插件列表"""
        common_plugins = [
            'Chrome PDF Plugin',
            'Chrome PDF Viewer',
            'Native Client',
            'Widevine Content Decryption Module',
            'Shockwave Flash'
        ]
        return random.sample(common_plugins, random.randint(3, len(common_plugins)))
    
    def _generate_webgl_info(self):
        """生成WebGL信息"""
        vendors = ['Google Inc.', 'Mozilla', 'WebKit']
        renderers = [
            'ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)'
        ]
        
        return {
            'vendor': random.choice(vendors),
            'renderer': random.choice(renderers)
        }
    
    def _generate_canvas_fingerprint(self):
        """生成Canvas指纹"""
        return {
            'hash': hashlib.md5(str(random.random()).encode()).hexdigest(),
            'data': base64.b64encode(str(random.random()).encode()).decode()
        }
    
    def _generate_audio_fingerprint(self):
        """生成音频指纹"""
        return {
            'hash': hashlib.sha256(str(random.random()).encode()).hexdigest()[:16],
            'oscillator': random.uniform(0.00001, 0.00009)
        }
    
    def setup_undetected_chrome(self):
        """设置不被检测的Chrome浏览器"""
        try:
            options = uc.ChromeOptions()
            
            # 基本反检测选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins-discovery')
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            
            # 高级反检测选项
            options.add_argument('--disable-automation')
            options.add_argument('--disable-browser-side-navigation')
            options.add_argument('--disable-dev-tools')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-infobars')
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-popup-blocking')
            
            # 随机窗口大小
            width = random.randint(1200, 1920)
            height = random.randint(800, 1080)
            options.add_argument(f'--window-size={width},{height}')
            
            # 随机User-Agent
            user_agent = self.ua.random
            options.add_argument(f'--user-agent={user_agent}')
            
            # 创建驱动
            self.driver = uc.Chrome(options=options, version_main=None)
            
            # 执行反检测脚本
            self._execute_anti_detection_scripts()
            
            logger.info("🚀 不被检测的Chrome浏览器设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置Chrome浏览器失败: {e}")
            return False
    
    def _execute_anti_detection_scripts(self):
        """执行反检测脚本"""
        if not self.driver:
            return
        
        # 隐藏webdriver属性
        self.driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        # 修改Chrome对象
        self.driver.execute_script("""
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
        """)
        
        # 修改权限查询
        self.driver.execute_script("""
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        # 修改插件信息
        fingerprint = self.generate_browser_fingerprint()
        self.driver.execute_script(f"""
            Object.defineProperty(navigator, 'plugins', {{
                get: () => {json.dumps(fingerprint['plugins'])},
            }});
        """)
        
        logger.info("🛡️ 反检测脚本执行完成")
    
    def human_like_behavior(self):
        """模拟人类行为"""
        if not self.driver:
            return
        
        try:
            # 随机鼠标移动
            actions = ActionChains(self.driver)
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                actions.move_by_offset(x, y)
                time.sleep(random.uniform(0.1, 0.3))
            actions.perform()
            
            # 随机滚动
            scroll_count = random.randint(1, 3)
            for _ in range(scroll_count):
                scroll_y = random.randint(100, 500)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_y});")
                time.sleep(random.uniform(0.5, 1.5))
            
            # 随机停留
            time.sleep(random.uniform(1, 3))
            
            logger.info("🤖 模拟人类行为完成")
            
        except Exception as e:
            logger.error(f"模拟人类行为失败: {e}")
    
    def advanced_phone_verification_bypass(self, url):
        """高级手机验证绕过"""
        logger.info(f"🎯 开始高级手机验证绕过: {url}")
        
        if not self.driver:
            if not self.setup_undetected_chrome():
                return False
        
        try:
            # 访问页面
            self.driver.get(url)
            time.sleep(random.uniform(2, 4))
            
            # 模拟人类行为
            self.human_like_behavior()
            
            # 检查是否为手机验证页面
            page_source = self.driver.page_source
            if self._is_phone_verification_page(page_source):
                logger.info("📱 检测到手机验证页面")
                
                # 保存页面内容用于分析
                with open('advanced_phone_verification_debug.html', 'w', encoding='utf-8') as f:
                    f.write(page_source)
                
                # 尝试多种绕过策略
                bypass_strategies = [
                    self._try_skip_verification,
                    self._try_fake_phone_submission,
                    self._try_browser_back,
                    self._try_direct_navigation,
                    self._try_cookie_manipulation,
                    self._try_javascript_bypass
                ]
                
                for i, strategy in enumerate(bypass_strategies, 1):
                    logger.info(f"🔄 尝试策略 {i}: {strategy.__name__}")
                    if strategy():
                        logger.info(f"✅ 策略 {i} 成功！")
                        return True
                    time.sleep(random.uniform(1, 2))
                
                logger.warning("❌ 所有绕过策略都失败了")
                return False
            else:
                logger.info("✅ 未检测到手机验证，页面正常")
                return True
                
        except Exception as e:
            logger.error(f"高级手机验证绕过失败: {e}")
            return False
    
    def _is_phone_verification_page(self, page_source):
        """检查是否为手机验证页面"""
        phone_keywords = [
            'phone number', 'mobile number', 'phone verification',
            'verify your phone', 'enter your phone', 'mobile verification',
            'phone number is required', 'verify phone number',
            'add phone number', 'phone number verification'
        ]
        
        page_text = page_source.lower()
        return any(keyword in page_text for keyword in phone_keywords)
    
    def _try_skip_verification(self):
        """尝试跳过验证"""
        try:
            skip_selectors = [
                "a[href*='skip']",
                "button[id*='skip']",
                "a[href*='later']",
                "button[id*='later']",
                "a[href*='not-now']",
                "button[id*='not-now']",
                "a:contains('Skip')",
                "a:contains('Later')",
                "a:contains('Not now')",
                "a:contains('Maybe later')"
            ]
            
            for selector in skip_selectors:
                try:
                    element = WebDriverWait(self.driver, 2).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()
                    time.sleep(2)
                    
                    # 检查是否成功跳过
                    if not self._is_phone_verification_page(self.driver.page_source):
                        return True
                except:
                    continue
            
            return False
        except Exception as e:
            logger.error(f"跳过验证失败: {e}")
            return False
    
    def _try_fake_phone_submission(self):
        """尝试提交虚假手机号"""
        try:
            # 查找手机号输入框
            phone_selectors = [
                "input[type='tel']",
                "input[name*='phone']",
                "input[id*='phone']",
                "input[placeholder*='phone']",
                "input[name*='mobile']",
                "input[id*='mobile']"
            ]
            
            phone_input = None
            for selector in phone_selectors:
                try:
                    phone_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if phone_input:
                # 生成虚假手机号
                fake_phones = [
                    '******-0123',
                    '555-0123',
                    '1234567890',
                    '******-555-0199',
                    '(*************'
                ]
                
                fake_phone = random.choice(fake_phones)
                phone_input.clear()
                phone_input.send_keys(fake_phone)
                
                # 查找提交按钮
                submit_selectors = [
                    "button[type='submit']",
                    "input[type='submit']",
                    "button:contains('Submit')",
                    "button:contains('Continue')",
                    "button:contains('Verify')"
                ]
                
                for selector in submit_selectors:
                    try:
                        submit_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        submit_btn.click()
                        time.sleep(3)
                        
                        # 检查是否成功
                        if not self._is_phone_verification_page(self.driver.page_source):
                            return True
                        break
                    except:
                        continue
            
            return False
        except Exception as e:
            logger.error(f"虚假手机号提交失败: {e}")
            return False
    
    def _try_browser_back(self):
        """尝试浏览器后退"""
        try:
            self.driver.back()
            time.sleep(2)
            return not self._is_phone_verification_page(self.driver.page_source)
        except Exception as e:
            logger.error(f"浏览器后退失败: {e}")
            return False
    
    def _try_direct_navigation(self):
        """尝试直接导航"""
        try:
            # 尝试导航到主页
            self.driver.get("https://www.amazon.com")
            time.sleep(2)
            return not self._is_phone_verification_page(self.driver.page_source)
        except Exception as e:
            logger.error(f"直接导航失败: {e}")
            return False
    
    def _try_cookie_manipulation(self):
        """尝试Cookie操作"""
        try:
            # 删除可能的验证相关Cookie
            cookies_to_delete = [
                'phone-verification',
                'verification-required',
                'mobile-verify',
                'phone-verify'
            ]
            
            for cookie_name in cookies_to_delete:
                try:
                    self.driver.delete_cookie(cookie_name)
                except:
                    pass
            
            # 刷新页面
            self.driver.refresh()
            time.sleep(2)
            
            return not self._is_phone_verification_page(self.driver.page_source)
        except Exception as e:
            logger.error(f"Cookie操作失败: {e}")
            return False
    
    def _try_javascript_bypass(self):
        """尝试JavaScript绕过"""
        try:
            # 执行JavaScript绕过脚本
            bypass_scripts = [
                "window.history.back();",
                "window.location.href = 'https://www.amazon.com';",
                "document.querySelector('form').style.display = 'none';",
                "window.location.reload();"
            ]
            
            for script in bypass_scripts:
                try:
                    self.driver.execute_script(script)
                    time.sleep(2)
                    
                    if not self._is_phone_verification_page(self.driver.page_source):
                        return True
                except:
                    continue
            
            return False
        except Exception as e:
            logger.error(f"JavaScript绕过失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("🧹 浏览器资源清理完成")
            except:
                pass

def test_advanced_system():
    """测试高级反检测系统"""
    system = AdvancedAntiDetectionSystem()
    
    try:
        # 测试URL
        test_urls = [
            "https://www.amazon.com/dp/B071P43VQD",
            "https://www.amazon.com/dp/B077YYF563",
            "https://www.amazon.com/dp/B07CXQ99P3"
        ]
        
        for url in test_urls:
            logger.info(f"🧪 测试URL: {url}")
            result = system.advanced_phone_verification_bypass(url)
            logger.info(f"结果: {'成功' if result else '失败'}")
            time.sleep(random.uniform(3, 6))
    
    finally:
        system.cleanup()

def integrate_with_existing_scraper():
    """与现有爬虫系统集成"""
    logger.info("🔗 开始与现有爬虫系统集成...")

    # 导入现有的优化版本
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("optimized_scraper", "优化版本_amazon_scraper.py")
        optimized_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(optimized_module)

        # 获取优化版本的爬虫类
        OptimizedAmazonScraper = optimized_module.OptimizedAmazonScraper

        # 创建增强版爬虫
        class EnhancedAmazonScraper(OptimizedAmazonScraper):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.anti_detection_system = AdvancedAntiDetectionSystem()

            def handle_phone_verification_enhanced(self, response, soup, start_time):
                """增强的手机验证处理"""
                logger.info("🚀 使用高级反检测系统处理手机验证")

                # 首先尝试原有的处理方法
                original_result = super()._handle_phone_verification(response, soup, start_time)

                if original_result.solved:
                    return original_result

                # 如果原有方法失败，使用高级反检测系统
                logger.info("🔄 原有方法失败，启用高级反检测系统")

                success = self.anti_detection_system.advanced_phone_verification_bypass(response.url)

                if success:
                    logger.info("✅ 高级反检测系统成功绕过手机验证")
                    return optimized_module.ChallengeResult(
                        solved=True,
                        method="高级反检测系统",
                        time_taken=time.time() - start_time,
                        details="使用undetected_chrome和多策略绕过"
                    )
                else:
                    logger.warning("❌ 高级反检测系统也无法绕过")
                    return original_result

            def cleanup(self):
                """清理资源"""
                super().cleanup()
                if hasattr(self, 'anti_detection_system'):
                    self.anti_detection_system.cleanup()

        logger.info("✅ 集成完成，增强版爬虫类已创建")
        return EnhancedAmazonScraper

    except Exception as e:
        logger.error(f"集成失败: {e}")
        return None

if __name__ == "__main__":
    # 可以选择运行测试或集成
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--integrate":
        integrate_with_existing_scraper()
    else:
        test_advanced_system()
