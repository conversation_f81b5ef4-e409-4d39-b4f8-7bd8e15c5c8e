#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试手机验证触发和破解
"""

import requests
import time
import logging
import random
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_verification_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def setup_session_with_proxy():
    """设置带代理的会话"""
    session = requests.Session()
    
    # 读取代理配置
    try:
        with open('proxies.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 解析第一个代理
        proxy_info = {}
        for line in lines[:5]:  # 读取前5行
            line = line.strip()
            if line.startswith('协议:'):
                proxy_info['protocol'] = line.split(':', 1)[1].strip()
            elif line.startswith('地址:'):
                proxy_info['host'] = line.split(':', 1)[1].strip()
            elif line.startswith('端口:'):
                proxy_info['port'] = line.split(':', 1)[1].strip()
            elif line.startswith('用户名:'):
                proxy_info['username'] = line.split(':', 1)[1].strip()
            elif line.startswith('密码:'):
                proxy_info['password'] = line.split(':', 1)[1].strip()
                break
        
        if all(k in proxy_info for k in ['protocol', 'host', 'port', 'username', 'password']):
            proxy_url = f"{proxy_info['protocol']}://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['host']}:{proxy_info['port']}"
            session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            logger.info(f"使用代理: {proxy_info['host']}:{proxy_info['port']}")
        else:
            logger.warning("代理配置不完整，使用直连")
    except Exception as e:
        logger.error(f"读取代理配置失败: {e}")
    
    # 设置请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    return session

def trigger_phone_verification(session, max_attempts=20):
    """尝试触发手机验证"""
    logger.info("🎯 开始尝试触发手机验证...")
    
    # 使用之前触发过手机验证的ASIN
    test_asins = [
        'B071P43VQD', 'B077YYF563', 'B07CXQ99P3', 'B0CCNVCKPX', 'B082BGM4H3',
        'B01B753NQY', 'B07PKC9K2D', 'B009GAZG22', 'B0C1KYJXL6', 'B08DL2D9YT',
        'B08P9411KS', 'B0CGCXY9P6', 'B097P8Q1G1', 'B017HTXTHC', 'B088TPQ5P5'
    ]
    
    for attempt in range(max_attempts):
        asin = random.choice(test_asins)
        url = f"https://www.amazon.com/dp/{asin}"
        
        logger.info(f"🔄 尝试 {attempt+1}/{max_attempts}: {asin}")
        
        try:
            # 快速连续请求以触发反爬虫
            for i in range(3):
                response = session.get(url, timeout=15)
                logger.info(f"  请求 {i+1}: 状态码 {response.status_code}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # 检查是否触发了手机验证
                    if is_phone_verification(response, soup):
                        logger.info("🎉 成功触发手机验证！")
                        return response, soup

                    # 检查其他验证类型
                    challenge_type = detect_challenge_type(response, soup)
                    if challenge_type:
                        logger.info(f"触发了其他验证: {challenge_type}")
                        # 如果检测到手机验证，立即返回进行分析
                        if challenge_type == 'PHONE_VERIFICATION':
                            logger.info("🎉 通过detect_challenge_type检测到手机验证！")
                            return response, soup
                
                # 短暂延迟
                time.sleep(0.5)
            
            # 稍长延迟避免过于频繁
            time.sleep(2)
            
        except Exception as e:
            logger.error(f"请求失败: {e}")
    
    logger.warning("未能触发手机验证")
    return None, None

def is_phone_verification(response, soup):
    """检测是否为手机验证页面"""
    url = response.url.lower()
    content = soup.get_text().lower()
    
    # URL检查
    if any(keyword in url for keyword in ['phone', 'mobile', 'verification', 'verify']):
        if 'phone' in url or 'mobile' in url:
            return True
    
    # 内容检查
    phone_keywords = [
        'phone number', 'mobile number', 'phone verification',
        'verify your phone', 'enter your phone', 'mobile verification',
        'phone number is required', 'verify phone number'
    ]
    
    for keyword in phone_keywords:
        if keyword in content:
            return True
    
    # 表单检查
    phone_inputs = soup.find_all('input', {'type': ['tel', 'text']})
    for inp in phone_inputs:
        name = inp.get('name', '').lower()
        placeholder = inp.get('placeholder', '').lower()
        if any(keyword in name or keyword in placeholder for keyword in ['phone', 'mobile']):
            return True
    
    return False

def detect_challenge_type(response, soup):
    """检测验证挑战类型"""
    url = response.url.lower()
    content = soup.get_text().lower()
    
    if 'captcha' in url or 'captcha' in content:
        return 'IMAGE_CAPTCHA'
    elif 'robot' in content and 'check' in content:
        return 'ROBOT_CHECK'
    elif 'continue shopping' in content:
        return 'CLICK_CONTINUE'
    elif any(keyword in content for keyword in ['phone', 'mobile']):
        return 'PHONE_VERIFICATION'
    elif 'email' in content and 'verification' in content:
        return 'EMAIL_VERIFICATION'
    
    return None

def analyze_phone_verification_page(response, soup):
    """分析手机验证页面"""
    logger.info("=" * 80)
    logger.info("🔍 手机验证页面详细分析")
    logger.info("=" * 80)
    
    # 基本信息
    logger.info(f"📍 URL: {response.url}")
    logger.info(f"📊 状态码: {response.status_code}")
    logger.info(f"📏 内容长度: {len(response.content)} bytes")
    
    # 页面标题
    title = soup.find('title')
    if title:
        logger.info(f"📄 页面标题: {title.get_text().strip()}")
    
    # 保存完整页面内容
    try:
        with open('phone_verification_debug.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        logger.info("💾 页面内容已保存到 phone_verification_debug.html")
    except Exception as e:
        logger.error(f"保存页面内容失败: {e}")
    
    # 查找表单
    forms = soup.find_all('form')
    logger.info(f"📝 找到 {len(forms)} 个表单")
    for i, form in enumerate(forms):
        action = form.get('action', '')
        method = form.get('method', 'GET')
        logger.info(f"  表单 {i+1}: {method} -> {action}")
        
        # 查找输入字段
        inputs = form.find_all('input')
        for inp in inputs:
            name = inp.get('name', '')
            input_type = inp.get('type', '')
            value = inp.get('value', '')
            placeholder = inp.get('placeholder', '')
            if name:
                logger.info(f"    输入字段: {name} ({input_type}) = '{value}' placeholder='{placeholder}'")
    
    # 查找手机号相关元素
    phone_elements = soup.find_all(['input', 'label', 'span'], string=re.compile(r'phone|mobile', re.I))
    phone_elements.extend(soup.find_all('input', {'type': 'tel'}))
    phone_elements.extend(soup.find_all('input', {'name': re.compile(r'phone|mobile', re.I)}))
    
    logger.info(f"📱 找到 {len(phone_elements)} 个手机号相关元素")
    for element in phone_elements:
        logger.info(f"  {element.name}: {element.get('name', '')} - {element.get_text()[:50]}")
    
    # 查找按钮
    buttons = soup.find_all(['button', 'input'], {'type': ['submit', 'button']})
    buttons.extend(soup.find_all('a', href=True))
    logger.info(f"🔘 找到 {len(buttons)} 个可点击元素")
    for button in buttons[:10]:  # 只显示前10个
        text = button.get_text().strip() or button.get('value', '') or button.get('href', '')
        logger.info(f"  {button.name}: {text[:50]}")
    
    # 查找跳过链接
    skip_patterns = [
        r'skip|later|not now|maybe later',
        r'continue without|without verification',
        r'i don\'t have|no phone|no mobile'
    ]
    
    skip_links = []
    for pattern in skip_patterns:
        skip_links.extend(soup.find_all('a', string=re.compile(pattern, re.I)))
        skip_links.extend(soup.find_all('a', {'title': re.compile(pattern, re.I)}))
    
    if skip_links:
        logger.info("⏭️ 找到可能的跳过链接:")
        for link in skip_links:
            href = link.get('href', '')
            text = link.get_text().strip()
            logger.info(f"  {text}: {href}")
    
    # 显示页面文本内容（前2000字符）
    page_text = soup.get_text().strip()
    logger.info("📄 页面文本内容（前2000字符）:")
    logger.info("-" * 40)
    logger.info(page_text[:2000])
    if len(page_text) > 2000:
        logger.info("... (内容被截断)")
    logger.info("-" * 40)
    
    logger.info("=" * 80)

def test_bypass_strategies(session, response, soup):
    """测试各种绕过策略"""
    logger.info("🚀 开始测试绕过策略...")
    
    strategies = [
        ("跳过链接", test_skip_links),
        ("虚假手机号", test_fake_phone),
        ("返回上一页", test_go_back),
        ("直接访问", test_direct_access)
    ]
    
    for strategy_name, strategy_func in strategies:
        logger.info(f"🔄 测试策略: {strategy_name}")
        try:
            if strategy_func(session, response, soup):
                logger.info(f"✅ 策略 '{strategy_name}' 成功！")
                return True
            else:
                logger.info(f"❌ 策略 '{strategy_name}' 失败")
        except Exception as e:
            logger.error(f"策略 '{strategy_name}' 出错: {e}")
    
    logger.warning("所有绕过策略都失败了")
    return False

def test_skip_links(session, response, soup):
    """测试跳过链接策略"""
    skip_patterns = [
        r'skip|later|not now',
        r'continue without|without verification',
        r'i don\'t have|no phone'
    ]
    
    for pattern in skip_patterns:
        links = soup.find_all('a', string=re.compile(pattern, re.I))
        for link in links:
            href = link.get('href')
            if href:
                try:
                    if not href.startswith('http'):
                        href = urljoin(response.url, href)
                    
                    logger.info(f"尝试跳过链接: {href}")
                    skip_response = session.get(href, timeout=15)
                    
                    if skip_response.status_code == 200:
                        if 'phone' not in skip_response.url.lower():
                            return True
                except Exception as e:
                    logger.error(f"跳过链接失败: {e}")
    
    return False

def test_fake_phone(session, response, soup):
    """测试虚假手机号策略"""
    form = soup.find('form')
    if not form:
        return False
    
    action = form.get('action', '')
    if not action.startswith('http'):
        action = urljoin(response.url, action)
    
    # 构建表单数据
    form_data = {}
    for hidden_input in form.find_all('input', {'type': 'hidden'}):
        name = hidden_input.get('name')
        value = hidden_input.get('value', '')
        if name:
            form_data[name] = value
    
    # 查找手机号输入框
    phone_input = form.find('input', {'type': ['tel', 'text']})
    if not phone_input:
        phone_input = form.find('input', {'name': re.compile(r'phone|mobile', re.I)})
    
    if phone_input:
        phone_name = phone_input.get('name')
        if phone_name:
            fake_phones = ['******-0123', '555-0123', '1234567890']
            
            for fake_phone in fake_phones:
                form_data[phone_name] = fake_phone
                
                try:
                    logger.info(f"尝试提交虚假手机号: {fake_phone}")
                    submit_response = session.post(action, data=form_data, timeout=15)
                    
                    if submit_response.status_code == 200:
                        if 'phone' not in submit_response.url.lower():
                            return True
                except Exception as e:
                    logger.error(f"提交虚假手机号失败: {e}")
    
    return False

def test_go_back(session, response, soup):
    """测试返回策略"""
    # 尝试访问主页
    try:
        back_response = session.get('https://www.amazon.com', timeout=15)
        if back_response.status_code == 200:
            if 'phone' not in back_response.url.lower():
                return True
    except Exception as e:
        logger.error(f"返回主页失败: {e}")
    
    return False

def test_direct_access(session, response, soup):
    """测试直接访问策略"""
    # 从URL中提取ASIN
    asin_match = re.search(r'/dp/([A-Z0-9]{10})', response.url)
    if asin_match:
        asin = asin_match.group(1)
        direct_url = f"https://www.amazon.com/dp/{asin}"
        
        try:
            logger.info(f"尝试直接访问: {direct_url}")
            direct_response = session.get(direct_url, timeout=15)
            
            if direct_response.status_code == 200:
                if 'phone' not in direct_response.url.lower():
                    direct_soup = BeautifulSoup(direct_response.content, 'html.parser')
                    if direct_soup.find('div', {'id': 'dp'}):
                        return True
        except Exception as e:
            logger.error(f"直接访问失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🎯 专门测试手机验证触发和破解")
    print("=" * 60)
    
    # 设置会话
    session = setup_session_with_proxy()
    
    # 尝试触发手机验证
    response, soup = trigger_phone_verification(session)
    
    if response and soup:
        print("🎉 成功触发手机验证！")
        
        # 分析页面
        analyze_phone_verification_page(response, soup)
        
        # 测试绕过策略
        if test_bypass_strategies(session, response, soup):
            print("✅ 成功绕过手机验证！")
        else:
            print("❌ 所有绕过策略都失败了")
            print("💡 请查看 phone_verification_debug.html 了解页面结构")
    else:
        print("❌ 未能触发手机验证")
        print("💡 可能需要:")
        print("   - 使用不同的代理")
        print("   - 增加请求频率")
        print("   - 尝试更多ASIN")

if __name__ == "__main__":
    main()
