#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化版本的亚马逊爬虫
演示主要的反爬虫优化功能
"""

import requests
import random
import time
import logging
from bs4 import BeautifulSoup
from amazoncaptcha import AmazonCaptcha
import tempfile
import os
import re
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleAntiDetection:
    """简化的反检测管理器"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    def get_headers(self):
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def get_delay(self):
        """获取随机延迟"""
        return random.uniform(1.5, 3.5)

class SimpleChallengeHandler:
    """简化的验证挑战处理器"""
    
    def __init__(self, session):
        self.session = session
    
    def detect_challenge(self, response):
        """检测验证挑战"""
        content = response.text.lower()
        url = response.url.lower()
        
        if 'captcha' in content or 'captcha' in url:
            return 'captcha'
        elif 'continue shopping' in content:
            return 'continue'
        elif 'robot' in content or 'unusual traffic' in content:
            return 'robot'
        elif response.status_code == 503:
            return 'blocked'
        
        return None
    
    def handle_captcha(self, response):
        """处理验证码"""
        try:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找验证码图片
            captcha_img = soup.find('img', {'alt': re.compile(r'captcha', re.I)})
            if not captcha_img:
                logger.error("未找到验证码图片")
                return False
            
            img_url = urljoin(response.url, captcha_img['src'])
            logger.info(f"找到验证码图片: {img_url}")
            
            # 下载验证码图片
            img_response = self.session.get(img_url, timeout=10)
            if img_response.status_code != 200:
                logger.error("下载验证码图片失败")
                return False
            
            # 保存图片并解析
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                tmp_file.write(img_response.content)
                tmp_file.flush()
                
                try:
                    captcha = AmazonCaptcha(tmp_file.name)
                    solution = captcha.solve()
                    logger.info(f"验证码解析结果: {solution}")
                    
                    if solution and solution != 'Not solved':
                        # 查找表单并提交
                        form = soup.find('form')
                        if form:
                            action = form.get('action', '')
                            if not action.startswith('http'):
                                action = urljoin(response.url, action)
                            
                            # 构建表单数据
                            form_data = {}
                            for input_tag in form.find_all('input'):
                                name = input_tag.get('name')
                                if name:
                                    if input_tag.get('type') == 'text':
                                        form_data[name] = solution
                                    else:
                                        form_data[name] = input_tag.get('value', '')
                            
                            logger.info(f"提交验证码到: {action}")
                            logger.info(f"表单数据: {form_data}")
                            
                            # 提交验证码
                            submit_response = self.session.post(action, data=form_data, timeout=15)
                            
                            if submit_response.status_code == 200:
                                # 检查是否还有验证码
                                if 'captcha' not in submit_response.url.lower():
                                    logger.info("验证码处理成功！")
                                    return True
                                else:
                                    logger.warning("验证码提交后仍有验证码页面")
                            else:
                                logger.error(f"验证码提交失败，状态码: {submit_response.status_code}")
                        else:
                            logger.error("未找到验证码表单")
                    else:
                        logger.error("验证码解析失败")
                        
                finally:
                    os.unlink(tmp_file.name)
            
            return False
            
        except Exception as e:
            logger.error(f"处理验证码时出错: {e}")
            return False
    
    def handle_continue(self, response):
        """处理继续购物验证"""
        try:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找继续链接
            continue_link = soup.find('a', string=re.compile(r'continue', re.I))
            if not continue_link:
                continue_link = soup.find('a', href=re.compile(r'continue', re.I))
            
            if continue_link and continue_link.get('href'):
                continue_url = urljoin(response.url, continue_link['href'])
                logger.info(f"找到继续链接: {continue_url}")
                
                # 等待一段时间模拟人类行为
                time.sleep(random.uniform(3, 6))
                
                continue_response = self.session.get(continue_url, timeout=15)
                
                if continue_response.status_code == 200:
                    logger.info("继续购物验证处理成功！")
                    return True
                else:
                    logger.error(f"继续购物验证失败，状态码: {continue_response.status_code}")
            else:
                logger.error("未找到继续链接")
            
            return False
            
        except Exception as e:
            logger.error(f"处理继续购物验证时出错: {e}")
            return False

class OptimizedScraper:
    """优化版爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.anti_detection = SimpleAntiDetection()
        self.challenge_handler = SimpleChallengeHandler(self.session)
        
        # 设置会话
        self.session.headers.update(self.anti_detection.get_headers())
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'challenges_detected': 0,
            'challenges_solved': 0
        }
    
    def make_request(self, url, max_retries=3):
        """发送请求并处理验证挑战"""
        for attempt in range(max_retries):
            try:
                # 添加随机延迟
                delay = self.anti_detection.get_delay()
                logger.info(f"请求 {url} 前等待 {delay:.2f} 秒...")
                time.sleep(delay)
                
                # 发送请求
                response = self.session.get(url, timeout=15)
                self.stats['total_requests'] += 1
                
                logger.info(f"请求完成，状态码: {response.status_code}")
                
                if response.status_code == 200:
                    # 检测验证挑战
                    challenge_type = self.challenge_handler.detect_challenge(response)
                    
                    if challenge_type:
                        self.stats['challenges_detected'] += 1
                        logger.warning(f"检测到验证挑战: {challenge_type}")
                        
                        # 处理验证挑战
                        if challenge_type == 'captcha':
                            if self.challenge_handler.handle_captcha(response):
                                self.stats['challenges_solved'] += 1
                                # 重新请求原始URL
                                return self.make_request(url, max_retries - attempt - 1)
                        elif challenge_type == 'continue':
                            if self.challenge_handler.handle_continue(response):
                                self.stats['challenges_solved'] += 1
                                # 重新请求原始URL
                                return self.make_request(url, max_retries - attempt - 1)
                        elif challenge_type == 'robot':
                            logger.error("检测到机器人检查，建议更换IP")
                            return None
                        elif challenge_type == 'blocked':
                            logger.error("IP被封锁")
                            return None
                        
                        logger.error(f"验证挑战处理失败: {challenge_type}")
                        return None
                    else:
                        self.stats['successful_requests'] += 1
                        return response
                else:
                    logger.error(f"请求失败，状态码: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"请求异常: {e}")
                
            # 重试前等待
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 5
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        return None
    
    def test_scraping(self, test_asins):
        """测试抓取功能"""
        logger.info(f"开始测试抓取 {len(test_asins)} 个ASIN")
        
        results = []
        
        for i, asin in enumerate(test_asins, 1):
            logger.info(f"处理第 {i}/{len(test_asins)} 个ASIN: {asin}")
            
            url = f"https://www.amazon.com/dp/{asin}"
            response = self.make_request(url)
            
            if response:
                # 简单提取产品信息
                soup = BeautifulSoup(response.content, 'html.parser')
                
                title_elem = soup.find('span', {'id': 'productTitle'})
                title = title_elem.get_text().strip() if title_elem else "未找到标题"
                
                result = {
                    'asin': asin,
                    'title': title,
                    'status': 'success'
                }
                
                results.append(result)
                logger.info(f"ASIN {asin} 处理成功: {title[:50]}...")
            else:
                logger.error(f"ASIN {asin} 处理失败")
                results.append({
                    'asin': asin,
                    'title': '',
                    'status': 'failed'
                })
        
        # 打印统计信息
        logger.info("=" * 50)
        logger.info("测试完成！统计信息:")
        logger.info(f"总请求数: {self.stats['total_requests']}")
        logger.info(f"成功请求数: {self.stats['successful_requests']}")
        logger.info(f"检测到的验证挑战: {self.stats['challenges_detected']}")
        logger.info(f"成功解决的验证挑战: {self.stats['challenges_solved']}")
        
        success_rate = self.stats['successful_requests'] / max(self.stats['total_requests'], 1) * 100
        challenge_solve_rate = self.stats['challenges_solved'] / max(self.stats['challenges_detected'], 1) * 100
        
        logger.info(f"请求成功率: {success_rate:.1f}%")
        logger.info(f"验证挑战解决率: {challenge_solve_rate:.1f}%")
        logger.info("=" * 50)
        
        return results

def main():
    """主函数"""
    # 测试ASIN列表（可以修改为你想测试的ASIN）
    test_asins = [
        'B000HJUXMO',  # 一个测试ASIN
        'B07G3KS4JK',  # 另一个测试ASIN
        'B097XQ49D4'   # 第三个测试ASIN
    ]
    
    print("=" * 60)
    print("优化版亚马逊爬虫测试")
    print("主要优化功能:")
    print("1. 智能验证码检测和处理")
    print("2. 多种验证挑战类型支持")
    print("3. 随机延迟和请求头")
    print("4. 自动重试机制")
    print("5. 详细的统计信息")
    print("=" * 60)
    
    scraper = OptimizedScraper()
    results = scraper.test_scraping(test_asins)
    
    print("\n处理结果:")
    for result in results:
        print(f"ASIN: {result['asin']}, 状态: {result['status']}, 标题: {result['title'][:50]}...")

if __name__ == "__main__":
    main()
