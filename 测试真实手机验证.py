#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的手机验证处理
"""

import sys
import os
import logging
import time
import requests
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_phone_verification():
    """测试真实的手机验证处理"""
    logger.info("🧪 开始测试真实手机验证处理...")
    
    try:
        from 优化版本_amazon_scraper import ChallengeHandler, ChallengeType, ProxyManager
        
        # 创建实例
        session = requests.Session()
        proxy_manager = ProxyManager()
        handler = ChallengeHandler(session, proxy_manager)
        
        # 创建一个更真实的Amazon手机验证页面
        real_amazon_content = '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Amazon Sign In</title>
        </head>
        <body>
            <div id="auth-error-message-box" class="a-box a-alert a-alert-error">
                <div class="a-box-inner a-alert-container">
                    <h4 class="a-alert-heading">There was a problem</h4>
                    <div class="a-alert-content">
                        We need to verify your identity. Please enter your phone number.
                    </div>
                </div>
            </div>
            
            <div class="a-section">
                <form name="signIn" method="post" action="/ap/signin" novalidate="">
                    <input type="hidden" name="appActionToken" value="abc123xyz789">
                    <input type="hidden" name="appAction" value="SIGNIN_PWD_COLLECT">
                    <input type="hidden" name="subPageType" value="SignInClaimCollect">
                    <input type="hidden" name="pageId" value="usflex">
                    <input type="hidden" name="openid.return_to" value="ape:aHR0cHM6Ly93d3cuYW1hem9uLmNvbS8=">
                    
                    <div class="a-section">
                        <label for="ap_phone_number">Mobile phone number</label>
                        <input type="tel" maxlength="50" id="ap_phone_number" name="phoneNumber" 
                               class="a-input-text a-span12 auth-autofocus auth-required-field" 
                               aria-describedby="phoneNumber-announce">
                    </div>
                    
                    <div class="a-section">
                        <span id="continue-announce" class="a-button a-button-span12 a-button-primary">
                            <span class="a-button-inner">
                                <input id="continue" class="a-button-input" type="submit" 
                                       aria-labelledby="continue-announce">
                                <span class="a-button-text" aria-hidden="true">Continue</span>
                            </span>
                        </span>
                    </div>
                    
                    <div class="a-section">
                        <a id="ap-account-fixup-phone-skip-link" 
                           href="/ap/signin?openid.return_to=ape%3AaHR0cHM6Ly93d3cuYW1hem9uLmNvbS8%3D&amp;pageId=usflex&amp;ignoreAuthState=1">
                            Skip for now
                        </a>
                    </div>
                </form>
            </div>
            
            <script>
                // Amazon's phone verification detection script
                if (window.location.href.includes('signin') && document.getElementById('ap_phone_number')) {
                    console.log('Phone verification page detected');
                }
            </script>
        </body>
        </html>
        '''
        
        # 创建真实的响应对象
        test_response = requests.Response()
        test_response.status_code = 200
        test_response.url = "https://www.amazon.com/ap/signin?openid.return_to=ape%3AaHR0cHM6Ly93d3cuYW1hem9uLmNvbS8%3D&pageId=usflex"
        test_response._content = real_amazon_content.encode('utf-8')
        test_response.headers['Content-Type'] = 'text/html; charset=utf-8'
        
        test_soup = BeautifulSoup(test_response.content, 'html.parser')
        
        logger.info("📱 测试真实Amazon手机验证页面...")
        logger.info(f"页面URL: {test_response.url}")
        logger.info(f"页面大小: {len(test_response.content)} bytes")
        
        # 测试挑战类型检测
        logger.info("🔍 测试挑战类型检测...")
        
        # 模拟挑战检测逻辑
        challenge_detected = False
        if any(keyword in test_response.text.lower() for keyword in [
            'phone number', 'mobile phone', 'verify your identity', 
            'ap_phone_number', 'phoneNumber'
        ]):
            challenge_detected = True
            logger.info("✅ 手机验证挑战检测成功")
        else:
            logger.error("❌ 手机验证挑战检测失败")
        
        if challenge_detected:
            # 测试handle_challenge方法
            logger.info("🎯 测试handle_challenge方法...")
            try:
                result = handler.handle_challenge(ChallengeType.PHONE_VERIFICATION, test_response, test_soup)
                logger.info(f"📊 挑战处理结果:")
                logger.info(f"   - 是否解决: {result.solved}")
                logger.info(f"   - 使用方法: {result.method_used}")
                logger.info(f"   - 耗时: {result.time_taken:.2f}秒")
                logger.info(f"   - 错误信息: {result.error_message}")
                
                if result.solved:
                    logger.info("🎉 手机验证破解成功！")
                else:
                    logger.warning(f"⚠️ 手机验证破解失败: {result.error_message}")
                    
            except Exception as e:
                logger.error(f"❌ handle_challenge方法调用失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
        
        # 测试直接调用_handle_phone_verification
        logger.info("📞 测试直接调用_handle_phone_verification...")
        try:
            start_time = time.time()
            direct_result = handler._handle_phone_verification(test_response, test_soup, start_time)
            logger.info(f"📊 直接调用结果:")
            logger.info(f"   - 是否解决: {direct_result.solved}")
            logger.info(f"   - 使用方法: {direct_result.method_used}")
            logger.info(f"   - 耗时: {direct_result.time_taken:.2f}秒")
            logger.info(f"   - 错误信息: {direct_result.error_message}")
            
        except Exception as e:
            logger.error(f"❌ 直接调用失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        # 测试各个破解策略
        logger.info("🔧 测试各个破解策略...")
        try:
            # 策略1: 跳过链接
            logger.info("📝 测试策略1: 跳过链接...")
            skip_result = handler._try_skip_phone_verification(test_response, test_soup)
            logger.info(f"跳过链接策略结果: {skip_result}")
            
            # 策略2: 虚假手机号
            logger.info("📱 测试策略2: 虚假手机号...")
            fake_phone_result = handler._try_fake_phone_number(test_response, test_soup)
            logger.info(f"虚假手机号策略结果: {fake_phone_result}")
            
        except Exception as e:
            logger.error(f"❌ 测试破解策略失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        logger.info("🎯 真实手机验证测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def test_exception_handling():
    """测试异常处理"""
    logger.info("🧪 测试异常处理...")
    
    try:
        from 优化版本_amazon_scraper import ChallengeHandler, ChallengeType, ProxyManager
        
        # 创建实例
        session = requests.Session()
        proxy_manager = ProxyManager()
        handler = ChallengeHandler(session, proxy_manager)
        
        # 创建一个会导致异常的响应
        bad_response = requests.Response()
        bad_response.status_code = 200
        bad_response.url = "https://www.amazon.com/ap/signin"
        bad_response._content = b'<html><body>Invalid content</body></html>'
        
        bad_soup = BeautifulSoup(bad_response.content, 'html.parser')
        
        # 测试异常处理
        logger.info("🎯 测试异常情况下的handle_challenge...")
        try:
            result = handler.handle_challenge(ChallengeType.PHONE_VERIFICATION, bad_response, bad_soup)
            logger.info(f"异常处理结果: solved={result.solved}, error={result.error_message}")
        except Exception as e:
            logger.error(f"异常处理测试失败: {e}")
            
    except Exception as e:
        logger.error(f"异常处理测试出错: {e}")

def main():
    """主函数"""
    print("🧪 真实手机验证测试工具")
    print("=" * 50)
    
    test_real_phone_verification()
    print("\n" + "=" * 50)
    
    test_exception_handling()
    print("\n" + "=" * 50)
    
    print("🎯 测试完成！")

if __name__ == "__main__":
    main()
