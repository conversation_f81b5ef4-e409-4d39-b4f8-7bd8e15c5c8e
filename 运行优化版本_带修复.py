#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行优化版本亚马逊爬虫（包含手机验证修复）
"""

import sys
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def apply_fixes():
    """应用修复补丁"""
    try:
        # 导入修复模块
        from 快速修复_手机验证 import apply_patches
        
        # 应用补丁
        success = apply_patches()
        if not success:
            logger.error("补丁应用失败")
            return False
        
        logger.info("修复补丁应用成功")
        return True
        
    except ImportError as e:
        logger.error(f"无法导入修复模块: {e}")
        return False
    except Exception as e:
        logger.error(f"应用修复时出错: {e}")
        return False

def run_scraper_with_fixes():
    """运行带修复的爬虫"""
    
    print("=" * 60)
    print("优化版亚马逊爬虫 - 包含手机验证修复")
    print("=" * 60)
    
    # 首先应用修复
    logger.info("正在应用修复补丁...")
    if not apply_fixes():
        print("❌ 修复补丁应用失败，无法继续运行")
        return
    
    print("✅ 修复补丁应用成功！")
    print("现在支持以下验证挑战类型:")
    print("  - 图片验证码 (IMAGE_CAPTCHA)")
    print("  - 点击继续 (CLICK_CONTINUE)")
    print("  - 机器人检查 (ROBOT_CHECK)")
    print("  - 手机验证 (PHONE_VERIFICATION) ✨ 新增 - 包含5种破解策略")
    print("  - 邮箱验证 (EMAIL_VERIFICATION) ✨ 新增")
    print()
    print("🚀 手机验证破解策略:")
    print("  1. 查找跳过链接 (Skip/Later/Not now)")
    print("  2. 尝试虚假手机号提交")
    print("  3. 返回上一页绕过")
    print("  4. 直接访问产品页面")
    print("  5. Selenium高级绕过")
    print("  📊 详细调试信息将保存到 phone_verification_debug.html")
    print()
    
    try:
        # 导入优化版本的主要类
        from 优化版本_amazon_scraper import OptimizedAmazonScraper, ScraperGUI
        import pandas as pd
        
        # 检查运行模式
        if len(sys.argv) > 1 and sys.argv[1] == '--cli':
            # 命令行模式
            print("🚀 启动命令行模式...")
            
            scraper = OptimizedAmazonScraper(max_workers=10)  # 降低并发数以提高稳定性
            
            # 加载ASIN
            asins = []
            if os.path.exists('asin.xlsx'):
                try:
                    df = pd.read_excel('asin.xlsx')
                    asins = df.iloc[:, 0].astype(str).tolist()
                    logger.info(f"从asin.xlsx加载了 {len(asins)} 个ASIN")
                except Exception as e:
                    logger.error(f"加载ASIN文件失败: {e}")
                    return
            else:
                logger.error("未找到asin.xlsx文件")
                return
            
            if asins:
                print(f"📋 开始处理 {len(asins)} 个ASIN")
                print("💡 提示: 遇到手机验证时会自动切换代理")
                print()
                
                # 处理ASIN
                results = scraper.process_asins(asins)
                
                # 保存结果
                scraper.save_results('amazon_results_fixed.xlsx')
                
                # 显示统计信息
                stats = scraper.get_stats()
                print("\n" + "=" * 50)
                print("📊 最终统计信息:")
                print(f"总请求数: {stats['total_requests']}")
                print(f"成功请求数: {stats['successful_requests']}")
                print(f"失败请求数: {stats['failed_requests']}")
                print(f"检测到的验证挑战: {stats['challenges_detected']}")
                print(f"成功解决的验证挑战: {stats['challenges_solved']}")
                print(f"代理切换次数: {stats['proxy_switches']}")
                print(f"处理的产品数: {stats['processed_products']}")
                print(f"成功率: {stats['success_rate']:.1f}%")
                print(f"验证挑战解决率: {stats['challenge_solve_rate']:.1f}%")
                print("=" * 50)
                
                if results:
                    print(f"✅ 成功处理 {len(results)} 个产品")
                    print("📁 结果已保存到 amazon_results_fixed.xlsx")
                else:
                    print("⚠️  没有成功处理任何产品")
            else:
                print("❌ 没有找到要处理的ASIN")
                
        else:
            # GUI模式
            print("🖥️  启动图形界面模式...")
            print("💡 提示: 在GUI中可以实时查看处理进度和日志")
            print()
            
            app = ScraperGUI()
            app.run()
            
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        print("❌ 无法导入必要的模块，请检查依赖是否安装完整")
    except Exception as e:
        logger.error(f"运行爬虫时出错: {e}")
        print(f"❌ 运行出错: {e}")

def show_usage():
    """显示使用说明"""
    print("""
使用方法:
    python 运行优化版本_带修复.py          # GUI模式
    python 运行优化版本_带修复.py --cli    # 命令行模式

新增功能:
    ✨ 自动处理手机验证挑战
    ✨ 自动处理邮箱验证挑战
    ✨ 智能代理冷却机制
    ✨ 更稳定的错误恢复

文件要求:
    - asin.xlsx: 包含要处理的ASIN列表
    - proxies.txt: 代理配置文件

输出文件:
    - amazon_results_fixed.xlsx: 处理结果
    - 详细的日志输出

注意事项:
    - 遇到手机验证时会自动切换代理
    - 建议使用高质量的住宅代理
    - 可以通过日志监控处理进度
""")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_usage()
    else:
        run_scraper_with_fixes()
