#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行增强版本的Amazon爬虫
集成了改进的手机验证处理
"""

import sys
import os
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("🚀 启动增强版本Amazon爬虫")
    print("=" * 60)
    
    try:
        # 1. 应用增强手机验证处理补丁
        logger.info("🔧 应用增强手机验证处理补丁...")
        from 增强手机验证处理 import patch_enhanced_phone_verification
        
        if not patch_enhanced_phone_verification():
            logger.error("❌ 增强补丁应用失败")
            return False
        
        logger.info("✅ 增强补丁应用成功")
        
        # 2. 导入并初始化爬虫
        logger.info("📦 导入优化版本爬虫...")
        from 优化版本_amazon_scraper import OptimizedAmazonScraper
        
        # 3. 创建爬虫实例
        logger.info("🔨 创建爬虫实例...")
        scraper = OptimizedAmazonScraper()
        
        # 4. 检查必要文件
        logger.info("📋 检查必要文件...")
        required_files = ['proxies.txt', 'asin.xlsx']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
            else:
                file_size = os.path.getsize(file)
                logger.info(f"   ✅ {file} ({file_size} bytes)")
        
        if missing_files:
            logger.error(f"❌ 缺少必要文件: {missing_files}")
            return False
        
        # 5. 显示配置信息
        logger.info("⚙️ 爬虫配置:")
        logger.info(f"   - 最大线程数: {getattr(scraper, 'max_workers', '未知')}")
        logger.info(f"   - 请求延迟: {getattr(scraper, 'min_delay', 1)}-{getattr(scraper, 'max_delay', 3)}秒")
        logger.info(f"   - 超时设置: {getattr(scraper, 'timeout', 30)}秒")
        logger.info(f"   - 重试次数: {getattr(scraper, 'max_retries', 3)}")
        
        # 6. 启动爬虫
        logger.info("🎯 启动爬虫...")
        print("\n" + "=" * 60)
        print("🔥 爬虫已启动！")
        print("📊 实时状态将显示在日志中")
        print("📁 结果将保存到 Brand_品牌产品.xlsx")
        print("🛑 按 Ctrl+C 可以安全停止爬虫")
        print("=" * 60)
        
        # 启动爬虫
        scraper.run()
        
        logger.info("🎉 爬虫运行完成")
        return True
        
    except KeyboardInterrupt:
        logger.info("🛑 用户中断爬虫运行")
        print("\n🛑 爬虫已安全停止")
        return True
        
    except Exception as e:
        logger.error(f"❌ 爬虫运行出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def show_status():
    """显示当前状态"""
    print("📊 增强版本Amazon爬虫状态检查")
    print("=" * 50)
    
    # 检查文件状态
    files_to_check = [
        'proxies.txt',
        'asin.xlsx', 
        '优化版本_amazon_scraper.py',
        '增强手机验证处理.py',
        'enhanced_scraper.log'
    ]
    
    print("📁 文件检查:")
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size} bytes)")
        else:
            print(f"   ❌ {file} (不存在)")
    
    # 检查日志文件
    log_files = ['enhanced_scraper.log', 'amazon_scraper.log', 'phone_verification_debug.html']
    print("\n📄 日志文件:")
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"   ✅ {log_file} ({size} bytes)")
        else:
            print(f"   ❌ {log_file} (不存在)")
    
    print("\n" + "=" * 50)

def test_enhanced_features():
    """测试增强功能"""
    print("🧪 测试增强功能")
    print("=" * 50)
    
    try:
        # 测试补丁应用
        logger.info("🔧 测试增强补丁...")
        from 增强手机验证处理 import patch_enhanced_phone_verification
        
        if patch_enhanced_phone_verification():
            print("✅ 增强补丁测试通过")
        else:
            print("❌ 增强补丁测试失败")
            return False
        
        # 测试爬虫导入
        logger.info("📦 测试爬虫导入...")
        from 优化版本_amazon_scraper import OptimizedAmazonScraper, ChallengeHandler, ChallengeType
        print("✅ 爬虫导入测试通过")
        
        # 测试增强方法
        logger.info("🔍 测试增强方法...")
        import requests
        from 优化版本_amazon_scraper import ProxyManager
        
        session = requests.Session()
        proxy_manager = ProxyManager()
        handler = ChallengeHandler(session, proxy_manager)
        
        # 检查增强方法是否存在
        enhanced_methods = [
            '_attempt_phone_verification_bypass_enhanced',
            '_try_skip_phone_verification_enhanced', 
            '_try_fake_phone_number_enhanced',
            '_use_selenium_bypass_enhanced'
        ]
        
        for method_name in enhanced_methods:
            if hasattr(handler, method_name):
                print(f"   ✅ {method_name}")
            else:
                print(f"   ❌ {method_name}")
        
        print("✅ 增强功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 增强功能测试失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "status":
            show_status()
        elif command == "test":
            test_enhanced_features()
        elif command == "run":
            main()
        else:
            print("❓ 未知命令")
            print("可用命令:")
            print("  python 运行增强版本.py run    - 运行爬虫")
            print("  python 运行增强版本.py status - 检查状态") 
            print("  python 运行增强版本.py test   - 测试功能")
    else:
        # 默认运行爬虫
        main()
